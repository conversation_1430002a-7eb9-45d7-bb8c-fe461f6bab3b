{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\petition_view\\petition_view\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\petition_view\\petition_view\\index.vue", "mtime": 1754110928697}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0VndfYWNjb3VudF9sb2FuIH0gZnJvbSAnQC9hcGkvcGV0aXRpb25fdmlldy9wZXRpdGlvbl92aWV3Jw0KaW1wb3J0IHsgbGlzdENhcl90ZWFtIH0gZnJvbSAnQC9hcGkvY2FyX3RlYW0vY2FyX3RlYW0nDQppbXBvcnQgeyBhZGRDYXJfb3JkZXIgfSBmcm9tICdAL2FwaS9jYXJfb3JkZXIvY2FyX29yZGVyJw0KaW1wb3J0IHsgTWVzc2FnZSB9IGZyb20gJ2VsZW1lbnQtdWknDQppbXBvcnQgeyBnZXRfYmFua19hY2NvdW50LCBkY19zdWJtaXRfb3JkZXIsIGxvYW5fY29tcGVuc2F0aW9uX29yZGVyIH0gZnJvbSAnQC9hcGkvdndfYWNjb3VudF9sb2FuL3Z3X2FjY291bnRfbG9hbicNCmltcG9ydCB7IGFkZERhaWx5X2V4cGVuc2VfYXBwcm92YWwgfSBmcm9tICdAL2FwaS9kYWlseV9leHBlbnNlX2FwcHJvdmFsL2RhaWx5X2V4cGVuc2VfYXBwcm92YWwnDQppbXBvcnQgdXNlckluZm8gZnJvbSAnQC9sYXlvdXQvY29tcG9uZW50cy9EaWFsb2cvdXNlckluZm8udnVlJw0KaW1wb3J0IGNhckluZm8gZnJvbSAnQC9sYXlvdXQvY29tcG9uZW50cy9EaWFsb2cvY2FySW5mby52dWUnDQppbXBvcnQgTG9hblJlbWluZGVyTG9nIGZyb20gJ0AvbGF5b3V0L2NvbXBvbmVudHMvRGlhbG9nL2xvYW5SZW1pbmRlckxvZy52dWUnDQppbXBvcnQgRGlzcGF0Y2hWZWhpY2xlRm9ybSBmcm9tICdAL2xheW91dC9jb21wb25lbnRzL0RpYWxvZy9kaXNwYXRjaFZlaGljbGVGb3JtLnZ1ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnVndfYWNjb3VudF9sb2FuJywNCiAgY29tcG9uZW50czogew0KICAgIHVzZXJJbmZvLA0KICAgIGNhckluZm8sDQogICAgTG9hblJlbWluZGVyTG9nLA0KICAgIERpc3BhdGNoVmVoaWNsZUZvcm0sDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIFZJRVfooajmoLzmlbDmja4NCiAgICAgIHZ3X2FjY291bnRfbG9hbkxpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogJycsDQogICAgICAvLyDmmK/lkKblvLnlh7rlj5Hotbfku6Plgb/lr7nor53moYYNCiAgICAgIGNvbW11dGVvcGVuOiBmYWxzZSwNCiAgICAgIHRyaWFsRm9ybToge30sDQogICAgICB0cmlhbEZvcm1wcmluY2lwYWw6IDAsDQogICAgICB0cmlhbEZvcm1ib3ZlcmR1ZUFtb3VudDogMCwNCiAgICAgIHRyaWFsRm9ybWludGVyZXN0OiAwLA0KICAgICAgdHJpYWxGb3JtYWxsOiAwLA0KICAgICAgdHJpYWxGb3JtZG92ZXJkdWVBbW91bnQ6IDAsDQogICAgICB0cmlhbEZvcm1saXF1aWRhdGVkRGFtYWdlczogMCwNCiAgICAgIHRyaWFsRm9ybXRvdGFsOiAwLA0KICAgICAgdHJpYWxGb3Jtb3RoZXJEZWJ0OiAwLA0KICAgICAgYWNjb3VudExpc3Q6IFtdLCAvL+mTtuihjOi0puaIt+WIl+ihqA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTUsDQogICAgICAgIGN1c3RvbWVyTmFtZTogbnVsbCwNCiAgICAgICAgYXBwbHlObzogbnVsbCwgLy8g5pu05paw5Li65ZCO56uv5a2X5q61DQogICAgICAgIHBsYXRlTm86IG51bGwsDQogICAgICAgIHNhbGVzbWFuOiBudWxsLA0KICAgICAgICBqZ05hbWU6IG51bGwsDQogICAgICAgIHBhcnRuZXJJZDogbnVsbCwNCiAgICAgICAgcGV0aXRpb25OYW1lOiBudWxsLA0KICAgICAgICBmb2xsb3dTdGF0dXM6IG51bGwsDQogICAgICAgIGRpc3BhdGNoU3RhdHVzOiBudWxsLA0KICAgICAgICBhbGxvY2F0aW9uVGltZTogbnVsbCwNCiAgICAgICAgc3RhcnRUaW1lOiBudWxsLA0KICAgICAgICBlbmRUaW1lOiBudWxsLA0KICAgICAgICBzbGlwcGFnZVN0YXR1czogMywNCiAgICAgICAgdGVhbUlkOiBudWxsLA0KICAgICAgICBnYXJhZ2VJZDogbnVsbCwNCiAgICAgICAgbGlicmFyeVN0YXR1czogbnVsbCwNCiAgICAgICAgaW5ib3VuZFRpbWU6IG51bGwsDQogICAgICAgIG91dGJvdW5kVGltZTogbnVsbCwNCiAgICAgICAgbG9jYXRpbmdDb21taXNzaW9uOiBudWxsLA0KICAgICAgICBHUFM6IG51bGwsDQogICAgICAgIGtleVN0YXR1czogbnVsbCwNCiAgICAgICAga2V5VGltZTogbnVsbCwNCiAgICAgICAgY29sbGVjdGlvbk1ldGhvZDogbnVsbCwNCiAgICAgICAgc3RhdHVzOiBudWxsLA0KICAgICAgICBrZXlQcm92aW5jZTogbnVsbCwNCiAgICAgICAga2V5Q2l0eTogbnVsbCwNCiAgICAgICAga2V5Qm9yb3VnaDogbnVsbCwNCiAgICAgICAga2V5QWRkcmVzczogbnVsbCwNCiAgICAgICAga2V5RGV0YWlsQWRkcmVzczogbnVsbCwNCiAgICAgICAgY2FyU3RhdHVzOiBudWxsLA0KICAgICAgICBpc0ZpbmRDYXI6IG51bGwsDQogICAgICB9LA0KICAgICAgYmFua0xpc3Q6IFsNCiAgICAgICAgeyB2YWx1ZTogJ0VPMDAwMDAwMTAnLCBsYWJlbDogJ+iLj+mTtumHkeennycgfSwNCiAgICAgICAgeyB2YWx1ZTogJ0lPMDAwMDAwMDYnLCBsYWJlbDogJ+a1meWVhumTtuihjCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJ0lPMDAwMDAwMDcnLCBsYWJlbDogJ+S4reWFs+adkemTtuihjCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJ0lPMDAwMDAwMDgnLCBsYWJlbDogJ+iTnea1t+mTtuihjCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJ0lPMDAwMDAwMDknLCBsYWJlbDogJ+WNjueRnumTtuihjCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJ0lPMDAwMDAwMTAnLCBsYWJlbDogJ+ealuaWsOenn+i1gScgfSwNCiAgICAgIF0sDQogICAgICBmb2xsb3dVcExpc3Q6IFsNCiAgICAgICAgeyBsYWJlbDogJ+aXoOazlei3n+i/mycsIHZhbHVlOiAxIH0sDQogICAgICAgIHsgbGFiZWw6ICfnuqblrprov5jmrL4nLCB2YWx1ZTogMiB9LA0KICAgICAgICB7IGxhYmVsOiAn57un57ut6IGU57O7JywgdmFsdWU6IDMgfSwNCiAgICAgIF0sDQogICAgICBkaXNwYXRjaExpc3Q6IFsNCiAgICAgICAgeyBsYWJlbDogJ+W+hea0vuWNlScsIHZhbHVlOiAxIH0sDQogICAgICAgIHsgbGFiZWw6ICfmib7ovabkuK0nLCB2YWx1ZTogMiB9LA0KICAgICAgICB7IGxhYmVsOiAn5bey5YWl5bqTJywgdmFsdWU6IDMgfSwNCiAgICAgICAgeyBsYWJlbDogJ+acqua0vuWNlScsIHZhbHVlOiA0IH0sDQogICAgICAgIHsgbGFiZWw6ICflt7LmkqTplIAnLCB2YWx1ZTogNSB9LA0KICAgICAgXSwNCiAgICAgIGNhclN0YXR1c0xpc3Q6IFsNCiAgICAgICAgeyBsYWJlbDogJ+ecgeWGheato+W4uOihjOmpticsIHZhbHVlOiAnMScgfSwNCiAgICAgICAgeyBsYWJlbDogJ+ecgeWkluato+W4uOihjOmpticsIHZhbHVlOiAnMicgfSwNCiAgICAgICAgeyBsYWJlbDogJ+aKteaKvCcsIHZhbHVlOiAnMycgfSwNCiAgICAgICAgeyBsYWJlbDogJ+eWkeS8vOaKteaKvCcsIHZhbHVlOiAnNCcgfSwNCiAgICAgICAgeyBsYWJlbDogJ+eWkeS8vOm7kei9picsIHZhbHVlOiAnNScgfSwNCiAgICAgICAgeyBsYWJlbDogJ+W3suWFpeW6kycsIHZhbHVlOiAnNicgfSwNCiAgICAgICAgeyBsYWJlbDogJ+i9puWcqOazlemZoicsIHZhbHVlOiAnNycgfSwNCiAgICAgICAgeyBsYWJlbDogJ+W3suazleaLjScsIHZhbHVlOiAnOCcgfSwNCiAgICAgICAgeyBsYWJlbDogJ+WNj+WVhuWNlui9picsIHZhbHVlOiAnOScgfSwNCiAgICAgIF0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHsNCiAgICAgICAgY3VzdG9tZXJOYW1lOiAnJywNCiAgICAgICAgbW9iaWxlUGhvbmU6ICcnLA0KICAgICAgICBjb250cmFjdElkOiAnJywNCiAgICAgICAgY3VzdG9tZXJJZDogJycsDQogICAgICAgIHBsYXRlTm86ICcnLA0KICAgICAgICBjYXJTdGF0dXM6ICcnLA0KICAgICAgICBjYXJEZXRhaWxBZGRyZXNzOiAnJywNCiAgICAgICAgZ3BzU3RhdHVzOiAnJywNCiAgICAgICAgZm9sbG93VXBUeXBlOiAnJywNCiAgICAgICAgYXBwbHlObzogJycsDQogICAgICAgIHRlYW1JZDogbnVsbCwNCiAgICAgICAgZ2FyYWdlSWQ6IG51bGwsDQogICAgICAgIGxpYnJhcnlTdGF0dXM6IG51bGwsDQogICAgICAgIGluYm91bmRUaW1lOiAnJywNCiAgICAgICAgb3V0Ym91bmRUaW1lOiAnJywNCiAgICAgICAgbG9jYXRpbmdDb21taXNzaW9uOiBudWxsLA0KICAgICAgICBrZXlTdGF0dXM6IG51bGwsDQogICAgICAgIGtleVRpbWU6ICcnLA0KICAgICAgICBjb2xsZWN0aW9uTWV0aG9kOiBudWxsLA0KICAgICAgICBzdGF0dXM6IG51bGwsDQogICAgICAgIGtleVByb3ZpbmNlOiAnJywNCiAgICAgICAga2V5Q2l0eTogJycsDQogICAgICAgIGtleUJvcm91Z2g6ICcnLA0KICAgICAgICBrZXlBZGRyZXNzOiAnJywNCiAgICAgICAga2V5RGV0YWlsQWRkcmVzczogJycsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqM6KeE5YiZDQogICAgICBydWxlczoge30sDQogICAgICByYWRpbzogMCwNCiAgICAgIC8vIOa3u+WKoOaWsOeahOaVsOaNruWxnuaApw0KICAgICAgY3VzdG9tZXJJbmZvOiB7IGN1c3RvbWVySWQ6ICcnLCBhcHBseUlkOiAnJyB9LA0KICAgICAgdXNlckluZm9WaXNpYmxlOiBmYWxzZSwNCiAgICAgIHBsYXRlTm86ICcnLA0KICAgICAgY2FySW5mb1Zpc2libGU6IGZhbHNlLA0KICAgICAgY3VycmVudFJvdzoge30sDQogICAgICBkaXNwYXRjaExvYW5JZDogbnVsbCwNCiAgICAgIC8vIOaXpeW4uOi0ueeUqOeUs+ivt+ebuOWFsw0KICAgICAgZGFpbHlFeHBlbnNlRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBjdXJyZW50TG9hblJvdzoge30sDQogICAgICBkYWlseUV4cGVuc2VGb3JtOiB7DQogICAgICAgIGxpdGlnYXRpb25DYXNlSWQ6IG51bGwsDQogICAgICAgIGxvYW5JZDogbnVsbCwNCiAgICAgICAgc3RhdHVzOiAyLCAvLyDkuIrorr/mj5DkuqQNCiAgICAgICAgZXhwZW5zZVR5cGU6ICcnLA0KICAgICAgICBleHBlbnNlQW1vdW50OiAnJywNCiAgICAgICAgZXhwZW5zZURhdGU6ICcnLA0KICAgICAgICBleHBlbnNlRGVzY3JpcHRpb246ICcnLA0KICAgICAgICByZWNlaXB0VXJsOiAnJywNCiAgICAgICAgYXBwbGljYW50SWQ6ICcnLA0KICAgICAgICBhcHBsaWNhbnROYW1lOiAnJywNCiAgICAgICAgYXBwbGljYXRpb25UaW1lOiAnJywNCiAgICAgICAgYXBwcm92YWxTdGF0dXM6ICcwJw0KICAgICAgfSwNCiAgICAgIGRhaWx5RXhwZW5zZVJ1bGVzOiB7DQogICAgICAgIGV4cGVuc2VUeXBlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqei0ueeUqOexu+WeiycsIHRyaWdnZXI6ICdjaGFuZ2UnIH1dLA0KICAgICAgICBleHBlbnNlQW1vdW50OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpei0ueeUqOmHkeminScsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgcGF0dGVybjogL15cZCsoXC5cZHsxLDJ9KT8kLywgbWVzc2FnZTogJ+ivt+i+k+WFpeato+ehrueahOmHkemineagvOW8jycsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIGV4cGVuc2VEYXRlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqei0ueeUqOWPkeeUn+aXpeacnycsIHRyaWdnZXI6ICdjaGFuZ2UnIH1dLA0KICAgICAgICBleHBlbnNlRGVzY3JpcHRpb246IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6LS555So6K+05piOJywgdHJpZ2dlcjogJ2JsdXInIH1dDQogICAgICB9DQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgdGhpcy5nZXRDYXJUZWFtKCkNCiAgICB0aGlzLmdldEJhbmtMaXN0KCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6JWSUVX5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGxpc3RWd19hY2NvdW50X2xvYW4odGhpcy5xdWVyeVBhcmFtcykNCiAgICAgICAgLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMudndfYWNjb3VudF9sb2FuTGlzdCA9IHJlc3BvbnNlLnJvd3MNCiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0pDQogICAgfSwNCiAgICBmeGpDb3VudCh2YWwpIHsNCiAgICAgIHRoaXMudHJpYWxGb3JtLmZ4ak1vbmV5ID0gTnVtYmVyKE51bWJlcih2YWwpICogdGhpcy50cmlhbEZvcm1hbGwpIC8gMTAwDQogICAgfSwNCiAgICBxZENvdW50KHZhbCkgew0KICAgICAgdGhpcy50cmlhbEZvcm0ucWRNb25leSA9IE51bWJlcihOdW1iZXIodmFsKSAqIHRoaXMudHJpYWxGb3JtYWxsKSAvIDEwMA0KICAgIH0sDQogICAgZ21qQ291bnQodmFsKSB7DQogICAgICB0aGlzLnRyaWFsRm9ybS5nbWpNb25leSA9IE51bWJlcihOdW1iZXIodmFsKSAqIHRoaXMudHJpYWxGb3JtYWxsKSAvIDEwMA0KICAgIH0sDQogICAga2pqQ291bnQodmFsKSB7DQogICAgICB0aGlzLnRyaWFsRm9ybS5rampNb25leSA9IE51bWJlcihOdW1iZXIodmFsKSAqIHRoaXMudHJpYWxGb3JtYWxsKSAvIDEwMA0KICAgIH0sDQogICAga2pjekNvdW50KHZhbCkgew0KICAgICAgdGhpcy50cmlhbEZvcm0ua2pjek1vbmV5ID0gTnVtYmVyKE51bWJlcih2YWwpICogdGhpcy50cmlhbEZvcm1hbGwpIC8gMTAwDQogICAgfSwNCiAgICBzYmN6Q291bnQodmFsKSB7DQogICAgICB0aGlzLnRyaWFsRm9ybS5zYmN6TW9uZXkgPSBOdW1iZXIoTnVtYmVyKHZhbCkgKiB0aGlzLnRyaWFsRm9ybWFsbCkgLyAxMDANCiAgICB9LA0KICAgIGhhbmRsZUlucHV0Myh2YWx1ZSkgew0KICAgICAgdGhpcy50cmlhbEZvcm1vdGhlckRlYnQgPSBOdW1iZXIodmFsdWUpDQogICAgICB0aGlzLnRyaWFsRm9ybXRvdGFsID0gTnVtYmVyKA0KICAgICAgICB0aGlzLnRyaWFsRm9ybWFsbCArIHRoaXMudHJpYWxGb3JtZG92ZXJkdWVBbW91bnQgKyB0aGlzLnRyaWFsRm9ybWxpcXVpZGF0ZWREYW1hZ2VzICsgdGhpcy50cmlhbEZvcm1vdGhlckRlYnQNCiAgICAgICkudG9GaXhlZCgyKQ0KICAgIH0sDQogICAgdHJpYWxTdWIoKSB7DQogICAgICB2YXIgZGF0YSA9IHsNCiAgICAgICAgYXBwbHlJZDogdGhpcy50cmlhbEZvcm0uYXBwbHlJZCwNCiAgICAgICAgaWQ6IHRoaXMudHJpYWxGb3JtLmlkLA0KICAgICAgICBsb2FuSWQ6IHRoaXMudHJpYWxGb3JtLmxvYW5JZCwNCiAgICAgICAgbG9hbkFtb3VudDogdGhpcy50cmlhbEZvcm0ubG9hbkFtb3VudCwNCiAgICAgICAgcGFydG5lcklkOiB0aGlzLnRyaWFsRm9ybS5wYXJ0bmVySWQsDQogICAgICB9DQogICAgICBkY19zdWJtaXRfb3JkZXIoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMudHJpYWxGb3JtcHJpbmNpcGFsID0gcmVzcG9uc2UuZGF0YS5wcmluY2lwYWwgfHwgMA0KICAgICAgICB0aGlzLnRyaWFsRm9ybS5kZWZhdWx0SW50ZXJlc3QgPSByZXNwb25zZS5kYXRhLmRlZmF1bHRJbnRlcmVzdCB8fCAwDQogICAgICAgIHRoaXMudHJpYWxGb3JtaW50ZXJlc3QgPSByZXNwb25zZS5kYXRhLmludGVyZXN0IHx8IDANCiAgICAgICAgdGhpcy50cmlhbEZvcm1hbGwgPSByZXNwb25zZS5kYXRhLmJ0b3RhbE1vbmV5IHx8IDANCiAgICAgICAgdGhpcy50cmlhbEZvcm1kb3ZlcmR1ZUFtb3VudCA9IHJlc3BvbnNlLmRhdGEuZHRvdGFsTW9uZXkgfHwgMA0KICAgICAgICB0aGlzLnRyaWFsRm9ybWxpcXVpZGF0ZWREYW1hZ2VzID0gcmVzcG9uc2UuZGF0YS5saXF1aWRhdGVkRGFtYWdlcyB8fCAwDQoNCiAgICAgICAgLy8gdGhpcy50cmlhbEZvcm1ib3ZlcmR1ZUFtb3VudCA9IE51bWJlcigNCiAgICAgICAgLy8gICB0aGlzLnRyaWFsRm9ybXByaW5jaXBhbCArDQogICAgICAgIC8vICAgICB0aGlzLnRyaWFsRm9ybWludGVyZXN0ICsNCiAgICAgICAgLy8gICAgIHRoaXMudHJpYWxGb3JtLmRlZmF1bHRJbnRlcmVzdA0KICAgICAgICAvLyApLnRvRml4ZWQoMik7DQogICAgICAgIC8vIHRoaXMudHJpYWxGb3JtYm92ZXJkdWVBbW91bnQgPSB0aGlzLnRyaWFsRm9ybWJvdmVyZHVlQW1vdW50DQogICAgICAgIHRoaXMudHJpYWxGb3JtdG90YWwgPSBOdW1iZXIoDQogICAgICAgICAgdGhpcy50cmlhbEZvcm1hbGwgKyB0aGlzLnRyaWFsRm9ybWRvdmVyZHVlQW1vdW50ICsgdGhpcy50cmlhbEZvcm1saXF1aWRhdGVkRGFtYWdlcyArIHRoaXMudHJpYWxGb3Jtb3RoZXJEZWJ0DQogICAgICAgICkudG9GaXhlZCgyKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldEJhbmtMaXN0KCkgew0KICAgICAgZ2V0X2JhbmtfYWNjb3VudCgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmFjY291bnRMaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldENhclRlYW0oKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICBsaXN0Q2FyX3RlYW0oeyBzdGF0dXM6IDEgfSkNCiAgICAgICAgLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMuY2FyX3RlYW0gPSByZXNwb25zZS5yb3dzDQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9KQ0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKGRpYWxvZykgew0KICAgICAgaWYgKGRpYWxvZyA9PT0gJ2NvbW11dGVvcGVuJykgew0KICAgICAgICB0aGlzLmNvbW11dGVvcGVuID0gZmFsc2UNCiAgICAgIH0NCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLiRyZWZzLmZvcm0/LnJlc2V0RmllbGRzKCkgLy8g6YeN572u6KGo5Y2V6aqM6K+BDQogICAgICB0aGlzLmZvcm0gPSB7fQ0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jYXJTdGF0dXMgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmlzRmluZENhciA9IG51bGwNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLmFsbG9jYXRpb25UaW1lKSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhcnRUaW1lID0gdGhpcy5xdWVyeVBhcmFtcy5hbGxvY2F0aW9uVGltZVswXQ0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmVuZFRpbWUgPSB0aGlzLnF1ZXJ5UGFyYW1zLmFsbG9jYXRpb25UaW1lWzFdDQogICAgICB9DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxDQogICAgICB0aGlzLmdldExpc3QoKQ0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmN1c3RvbWVyTmFtZSA9IG51bGwNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY2VydElkID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wbGF0ZU5vID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zYWxlc21hbiA9IG51bGwNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuamdOYW1lID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJ0bmVySWQgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBldGl0aW9uTmFtZSA9IG51bGwNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZm9sbG93U3RhdHVzID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kaXNwYXRjaFN0YXR1cyA9IG51bGwNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuYWxsb2NhdGlvblRpbWUgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN0YXJ0VGltZSA9IG51bGwNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW5kVGltZSA9IG51bGwNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY2FyU3RhdHVzID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pc0ZpbmRDYXIgPSBudWxsDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIC8vIFRoaXMgbWV0aG9kIGlzIG5vIGxvbmdlciBuZWVkZWQgYXMgc2VsZWN0aW9uIGlzIHJlbW92ZWQuDQogICAgICAvLyBLZWVwaW5nIGl0IGZvciBub3cgYXMgaXQgbWlnaHQgYmUgdXNlZCBlbHNld2hlcmUgb3IgZm9yIGZ1dHVyZSBjaGFuZ2VzLg0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgIHRoaXMudGl0bGUgPSAn5L+u5pS5VklFVycNCiAgICB9LA0KICAgIC8qKiDlj5Hotbfku6Plgb/mk43kvZwgKi8NCiAgICBpbml0aWF0ZShyb3cpIHsNCiAgICAgIC8v5Y+R6LW35Luj5YG/DQogICAgICB2YXIgZGF0YSA9IHsNCiAgICAgICAgbG9hbklkOiByb3cubG9hbklkLA0KICAgICAgfQ0KICAgICAgbG9hbl9jb21wZW5zYXRpb25fb3JkZXIoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy50cmlhbEZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgdGhpcy50cmlhbEZvcm0ubG9hbkFtb3VudCA9IHJvdy5jb250cmFjdEFtdCB8fCAwDQogICAgICAgICAgdGhpcy50cmlhbEZvcm0uZHRvdGFsTW9uZXkgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmR0b3RhbE1vbmV5IDogMA0KICAgICAgICAgIHRoaXMudHJpYWxGb3JtcHJpbmNpcGFsID0gcmVzcG9uc2UuZGF0YS50cmlhbEJhbGFuY2UgPyByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZS5wcmluY2lwYWwgOiAwDQogICAgICAgICAgdGhpcy50cmlhbEZvcm1ib3ZlcmR1ZUFtb3VudCA9IHJvdy5ib3ZlcmR1ZUFtb3VudCB8fCAwDQogICAgICAgICAgdGhpcy50cmlhbEZvcm1pbnRlcmVzdCA9IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlID8gcmVzcG9uc2UuZGF0YS50cmlhbEJhbGFuY2UuaW50ZXJlc3QgOiAwDQogICAgICAgICAgdGhpcy50cmlhbEZvcm1hbGwgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmJ0b3RhbE1vbmV5IDogMA0KICAgICAgICAgIHRoaXMudHJpYWxGb3JtZG92ZXJkdWVBbW91bnQgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmR0b3RhbE1vbmV5IDogMA0KICAgICAgICAgIHRoaXMudHJpYWxGb3JtbGlxdWlkYXRlZERhbWFnZXMgPSByZXNwb25zZS5kYXRhLnRyaWFsQmFsYW5jZSA/IHJlc3BvbnNlLmRhdGEudHJpYWxCYWxhbmNlLmxpcXVpZGF0ZWREYW1hZ2VzIDogMA0KICAgICAgICAgIHRoaXMudHJpYWxGb3Jtb3RoZXJEZWJ0ID0gcmVzcG9uc2UuZGF0YS5vdGhlckRlYnQgPyByZXNwb25zZS5kYXRhLm90aGVyRGVidCA6IDANCiAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLnRyaWFsRm9ybWFsbCwgdGhpcy50cmlhbEZvcm1kb3ZlcmR1ZUFtb3VudCwgdGhpcy50cmlhbEZvcm1saXF1aWRhdGVkRGFtYWdlcywgdGhpcy50cmlhbEZvcm1vdGhlckRlYnQpDQogICAgICAgICAgdGhpcy50cmlhbEZvcm10b3RhbCA9IE51bWJlcigNCiAgICAgICAgICAgIHRoaXMudHJpYWxGb3JtYWxsICsgdGhpcy50cmlhbEZvcm1kb3ZlcmR1ZUFtb3VudCArIHRoaXMudHJpYWxGb3JtbGlxdWlkYXRlZERhbWFnZXMgKyB0aGlzLnRyaWFsRm9ybW90aGVyRGVidA0KICAgICAgICAgICkudG9GaXhlZCgyKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuYWRkVHJpYWwocm93KQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuY29tbXV0ZW9wZW4gPSB0cnVlDQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICAvLyDosIPnlKjlkI7nq6/mjqXlj6Pmt7vliqDmib7ovaborqLljZUNCiAgICAgICAgICBhZGRDYXJfb3JkZXIodGhpcy5mb3JtKQ0KICAgICAgICAgICAgLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICAgICAgTWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5om+6L2m6K6i5Y2V5re75Yqg5oiQ5YqfJywNCiAgICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywNCiAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAzICogMTAwMCwNCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpIC8vIOWIt+aWsOWIl+ihqA0KICAgICAgICAgICAgICAgIHRoaXMucmVzZXQoKSAvLyDph43nva7ooajljZUNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICBNZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfmt7vliqDlpLHotKU6ICcgKyByZXNwb25zZS5tZXNzYWdlLA0KICAgICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywNCiAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAzICogMTAwMCwNCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICAgICAgTWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+acjeWKoeWZqOmUmeivrzogJyArIGVycm9yLm1lc3NhZ2UsDQogICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywNCiAgICAgICAgICAgICAgICBkdXJhdGlvbjogMyAqIDEwMDAsDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9KQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUubG9nKCfooajljZXpqozor4HlpLHotKUnKQ0KICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5re75Yqg5paw55qE5pa55rOVDQogICAgb3BlblVzZXJJbmZvKGN1c3RvbWVySW5mbykgew0KICAgICAgdGhpcy5jdXN0b21lckluZm8gPSBjdXN0b21lckluZm8NCiAgICAgIHRoaXMudXNlckluZm9WaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQogICAgb3BlbkNhckluZm8ocGxhdGVObykgew0KICAgICAgdGhpcy5wbGF0ZU5vID0gcGxhdGVObw0KICAgICAgdGhpcy5jYXJJbmZvVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIC8vIOafpeeci+WCrOiusOaXpeW/lw0KICAgIGxvZ1ZpZXcocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRSb3cgPSByb3cNCiAgICAgIHRoaXMuJHJlZnMubG9hblJlbWluZGVyTG9nLm9wZW5Mb2dEaWFsb2coKQ0KICAgIH0sDQogICAgb3BlbkRpc3BhdGNoVmVoaWNsZUZvcm0ocm93KSB7DQogICAgICB0aGlzLmRpc3BhdGNoTG9hbklkID0gcm93LmxvYW5JZA0KICAgICAgdGhpcy4kcmVmcy5kaXNwYXRjaFZlaGljbGVGb3JtLm9wZW5EaWFsb2coKQ0KICAgIH0sDQogICAgLy8g5omT5byA5pel5bi46LS555So55Sz6K+35by556qXDQogICAgb3BlbkRhaWx5RXhwZW5zZURpYWxvZyhyb3cpIHsNCiAgICAgIHRoaXMucmVzZXREYWlseUV4cGVuc2VGb3JtKCkNCiAgICAgIHRoaXMuZGFpbHlFeHBlbnNlRm9ybS5hcHBsaWNhbnRJZCA9IFN0cmluZyh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyPy5pZCB8fCAnJykNCiAgICAgIHRoaXMuZGFpbHlFeHBlbnNlRm9ybS5hcHBsaWNhbnROYW1lID0gU3RyaW5nKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXI/Lm5hbWUgfHwgJycpDQogICAgICAvLyDorr7nva7lvZPliY3ml6XmnJ/kvZzkuLrnlLPor7fml7bpl7QNCiAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKQ0KICAgICAgdGhpcy5kYWlseUV4cGVuc2VGb3JtLmFwcGxpY2F0aW9uVGltZSA9IHRvZGF5LnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXQ0KICAgICAgLy8g6K6+572u6LS35qy+SUTlkoznirbmgIHvvIjkuIrorr/mj5DkuqTvvIkNCiAgICAgIHRoaXMuZGFpbHlFeHBlbnNlRm9ybS5sb2FuSWQgPSByb3cubG9hbklkDQogICAgICB0aGlzLmRhaWx5RXhwZW5zZUZvcm0uc3RhdHVzID0gMiAvLyDkuIrorr/mj5DkuqQNCiAgICAgIHRoaXMuY3VycmVudExvYW5Sb3cgPSByb3cNCiAgICAgIHRoaXMuZGFpbHlFeHBlbnNlRGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIC8vIOmHjee9ruaXpeW4uOi0ueeUqOihqOWNlQ0KICAgIHJlc2V0RGFpbHlFeHBlbnNlRm9ybSgpIHsNCiAgICAgIHRoaXMuZGFpbHlFeHBlbnNlRm9ybSA9IHsNCiAgICAgICAgbGl0aWdhdGlvbkNhc2VJZDogbnVsbCwNCiAgICAgICAgbG9hbklkOiBudWxsLA0KICAgICAgICBzdGF0dXM6IDIsIC8vIOS4iuiuv+aPkOS6pA0KICAgICAgICBleHBlbnNlVHlwZTogJycsDQogICAgICAgIGV4cGVuc2VBbW91bnQ6ICcnLA0KICAgICAgICBleHBlbnNlRGF0ZTogJycsDQogICAgICAgIGV4cGVuc2VEZXNjcmlwdGlvbjogJycsDQogICAgICAgIHJlY2VpcHRVcmw6ICcnLA0KICAgICAgICBhcHBsaWNhbnRJZDogJycsDQogICAgICAgIGFwcGxpY2FudE5hbWU6ICcnLA0KICAgICAgICBhcHBsaWNhdGlvblRpbWU6ICcnLA0KICAgICAgICBhcHByb3ZhbFN0YXR1czogJzAnDQogICAgICB9DQogICAgICB0aGlzLmN1cnJlbnRMb2FuUm93ID0ge30NCiAgICAgIHRoaXMuJHJlZnMuZGFpbHlFeHBlbnNlRm9ybT8ucmVzZXRGaWVsZHMoKQ0KICAgIH0sDQogICAgLy8g5Y+W5raI5pel5bi46LS555So55Sz6K+3DQogICAgY2FuY2VsRGFpbHlFeHBlbnNlKCkgew0KICAgICAgdGhpcy5kYWlseUV4cGVuc2VEaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgIHRoaXMucmVzZXREYWlseUV4cGVuc2VGb3JtKCkNCiAgICB9LA0KICAgIC8vIOaPkOS6pOaXpeW4uOi0ueeUqOeUs+ivtw0KICAgIHN1Ym1pdERhaWx5RXhwZW5zZSgpIHsNCiAgICAgIHRoaXMuJHJlZnMuZGFpbHlFeHBlbnNlRm9ybS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgLy8g56Gu5L+dIGxpdGlnYXRpb25DYXNlSWQg5pyJ5YC877yM5aaC5p6c5rKh5pyJ5YiZ6K6+572u5Li6IDANCiAgICAgICAgICBjb25zdCBzdWJtaXREYXRhID0geyAuLi50aGlzLmRhaWx5RXhwZW5zZUZvcm0gfQ0KICAgICAgICAgIGlmICghc3VibWl0RGF0YS5saXRpZ2F0aW9uQ2FzZUlkKSB7DQogICAgICAgICAgICBzdWJtaXREYXRhLmxpdGlnYXRpb25DYXNlSWQgPSAwDQogICAgICAgICAgfQ0KICAgICAgICAgIGFkZERhaWx5X2V4cGVuc2VfYXBwcm92YWwoc3VibWl0RGF0YSkudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfml6XluLjotLnnlKjnlLPor7fmj5DkuqTmiJDlip8nKQ0KICAgICAgICAgICAgICB0aGlzLmRhaWx5RXhwZW5zZURpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgICAgICAgICB0aGlzLnJlc2V0RGFpbHlFeHBlbnNlRm9ybSgpDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmj5DkuqTlpLHotKXvvJonICsgKHJlcy5tc2cgfHwgJ+acquefpemUmeivrycpKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aPkOS6pOWksei0pTonLCBlcnJvcikNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aPkOS6pOWksei0pe+8jOivt+eojeWQjumHjeivlScpDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOiOt+WPluaXpeW4uOi0ueeUqOW8ueeql+agh+mimA0KICAgIGdldERhaWx5RXhwZW5zZURpYWxvZ1RpdGxlKCkgew0KICAgICAgaWYgKHRoaXMuY3VycmVudExvYW5Sb3cgJiYgdGhpcy5jdXJyZW50TG9hblJvdy5jdXN0b21lck5hbWUpIHsNCiAgICAgICAgcmV0dXJuIGDmj5DkuqTml6XluLjotLnnlKggLSAke3RoaXMuY3VycmVudExvYW5Sb3cuY3VzdG9tZXJOYW1lfWANCiAgICAgIH0NCiAgICAgIHJldHVybiAn5o+Q5Lqk5pel5bi46LS555SoJw0KICAgIH0NCiAgfSwNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6d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file": "index.vue", "sourceRoot": "src/views/petition_view/petition_view", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"贷款人账户、姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"certId\">\r\n        <el-input v-model=\"queryParams.certId\" placeholder=\"贷款人身份证号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"plateNo\">\r\n        <el-input v-model=\"queryParams.plateNo\" placeholder=\"车牌号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"salesman\">\r\n        <el-input v-model=\"queryParams.salesman\" placeholder=\"业务员姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input v-model=\"queryParams.jgName\" placeholder=\"录单渠道名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"partnerId\">\r\n        <el-select v-model=\"queryParams.partnerId\" placeholder=\"放款银行\" clearable>\r\n          <el-option v-for=\"dict in bankList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"petitionName\">\r\n        <el-input v-model=\"queryParams.petitionName\" placeholder=\"上访员姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"followStatus\">\r\n        <el-select v-model=\"queryParams.followStatus\" placeholder=\"跟催类型\" clearable>\r\n          <el-option v-for=\"dict in followUpList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"dispatchStatus\">\r\n        <el-select v-model=\"queryParams.dispatchStatus\" placeholder=\"派单状态\" clearable>\r\n          <el-option v-for=\"dict in dispatchList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"指派时间\">\r\n        <el-date-picker\r\n          v-model=\"queryParams.allocationTime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"车辆状态\">\r\n        <el-select v-model=\"queryParams.carStatus\" placeholder=\"车辆状态\" clearable>\r\n          <el-option v-for=\"dict in carStatusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否派单找车\">\r\n        <el-select v-model=\"queryParams.isFindCar\" placeholder=\"是否派单找车\" clearable>\r\n          <el-option label=\"未派单\" :value=\"0\" />\r\n          <el-option label=\"已派单\" :value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"vw_account_loanList\">\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"55\" fixed=\"left\" />\r\n      <el-table-column label=\"逾期状态\" align=\"center\" prop=\"slippageStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span>\r\n            {{\r\n              scope.row.slippageStatus == 1\r\n                ? '提醒'\r\n                : scope.row.slippageStatus == 2\r\n                  ? '电催'\r\n                  : scope.row.slippageStatus == 3\r\n                    ? '上访'\r\n                    : scope.row.slippageStatus == 4\r\n                      ? '逾期30-60'\r\n                      : '逾期60+'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"还款状态\" align=\"center\" prop=\"repaymentStatus\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span>\r\n            {{\r\n              scope.row.repaymentStatus == 1\r\n                ? '还款中'\r\n                : scope.row.repaymentStatus == 2\r\n                  ? '已完结'\r\n                  : scope.row.repaymentStatus == 3\r\n                    ? '提前结清'\r\n                    : scope.row.repaymentStatus == 4\r\n                      ? '逾期催回结清'\r\n                      : scope.row.repaymentStatus == 5\r\n                        ? '逾期减免结清'\r\n                        : scope.row.repaymentStatus == 6\r\n                          ? '逾期未还款'\r\n                          : scope.row.repaymentStatus == 7\r\n                            ? '逾期还款中'\r\n                            : scope.row.repaymentStatus == 8\r\n                              ? '代偿未还款'\r\n                              : scope.row.repaymentStatus == 9\r\n                                ? '代偿还款中'\r\n                                : scope.row.repaymentStatus == 10\r\n                                  ? '代偿减免结清'\r\n                                  : scope.row.repaymentStatus == 11\r\n                                    ? '代偿全额结清'\r\n                                    : '未知状态'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"贷款人\" align=\"center\" prop=\"customerName\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"openUserInfo({ customerId: scope.row.customerId, applyId: scope.row.applyId })\">\r\n            {{ scope.row.customerName }}\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\" />\r\n      <el-table-column label=\"业务员\" align=\"center\" prop=\"nickName\" />\r\n      <el-table-column label=\"车牌号码\" align=\"center\" prop=\"plateNo\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"openCarInfo(scope.row.plateNo)\">{{ scope.row.plateNo }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"车辆状态\" align=\"center\" prop=\"carStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.carStatus != null\">\r\n            {{\r\n              (carStatusList.find(item => item.value === String(scope.row.carStatus)) || {}).label || ''\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"派车团队\" align=\"center\" prop=\"carTeamName\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.carTeamName\">{{ scope.row.carTeamName }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"位置\" align=\"center\" prop=\"carDetailAddress\" />\r\n      <el-table-column label=\"GPS状态\" align=\"center\" prop=\"gpsStatus\" />\r\n      <el-table-column label=\"派车单状态\" align=\"center\" prop=\"loanStatus\" width=\"130\" />\r\n      <el-table-column label=\"放款银行\" align=\"center\" prop=\"orgName\" />\r\n      <el-table-column label=\"逾期天数\" align=\"center\" prop=\"boverdueDays\" width=\"130\" />\r\n      <el-table-column label=\"首期逾期金额\" align=\"center\" prop=\"foverdueAmount\" width=\"130\" />\r\n      <el-table-column label=\"银行逾期金额\" align=\"center\" prop=\"boverdueAmount\" width=\"130\" />\r\n      <el-table-column label=\"代扣逾期金额\" align=\"center\" prop=\"doverdueAmount\" width=\"130\" />\r\n      <el-table-column label=\"银行结清金额\" align=\"center\" prop=\"bSettleAmount\" width=\"130\" />\r\n      <el-table-column label=\"代扣结清金额\" align=\"center\" prop=\"dSettleAmount\" width=\"130\" />\r\n      <el-table-column label=\"上访员\" align=\"center\" prop=\"petitionUser\" width=\"130\" />\r\n      <el-table-column label=\"跟催类型\" align=\"center\" prop=\"urgeType\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.urgeType != null\">\r\n            {{ scope.row.urgeType == 1 ? '继续联系' : scope.row.urgeType == 2 ? '约定还款' : '无法跟进' }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"催记日期\" align=\"center\" prop=\"assignTime\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.assignTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"分配时间\" align=\"center\" prop=\"petitionTime\" width=\"130\" />\r\n      <el-table-column label=\"下次跟进时间\" align=\"center\" prop=\"nextFollowTime\" width=\"130\" />\r\n      <el-table-column label=\"预扣款时间\" align=\"center\" prop=\"preDeductionTime\" width=\"130\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-popover placement=\"left\" trigger=\"click\" popper-class=\"custom-popover\">\r\n            <div class=\"operation-buttons\">\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"initiate(scope.row)\"\r\n                v-hasPermi=\"['vw_account_loan:vw_account_loan:edit']\">\r\n                发起代偿\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openDispatchVehicleForm(scope.row)\"\r\n                v-hasPermi=\"['vw_account_loan:vw_account_loan:edit']\">\r\n                发起找车\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"logView(scope.row)\"\r\n                v-hasPermi=\"['vw_account_loan:vw_account_loan:edit']\">\r\n                查看催记\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openDailyExpenseDialog(scope.row)\"\r\n                v-hasPermi=\"['daily_expense_approval:daily_expense_approval:add']\">\r\n                提交日常费用\r\n              </el-button>\r\n            </div>\r\n            <el-button slot=\"reference\" size=\"mini\" type=\"text\">更多</el-button>\r\n          </el-popover>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n\r\n    <!-- 发起代偿对话框 -->\r\n    <el-dialog :close-on-click-modal=\"false\" class=\"dialogBox\" title=\"发起代偿\" :visible.sync=\"commuteopen\" width=\"900px\" append-to-body>\r\n      <div class=\"settle_money\" @click=\"trialSub\">发起试算</div>\r\n      <el-form ref=\"trialForm\" :model=\"trialForm\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"贷款人\" prop=\"customerName\">\r\n              <el-input v-model=\"trialForm.customerName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"出单渠道\" prop=\"orgName\">\r\n              <el-input v-model=\"trialForm.orgName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款银行\" prop=\"bank\">\r\n              <el-input v-model=\"trialForm.bank\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款金额\" prop=\"loanAmount\">\r\n              <el-input v-model=\"trialForm.loanAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"剩余本金\">\r\n              <el-input v-model=\"trialFormprincipal\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行逾期金额\">\r\n              <el-input v-model=\"trialFormboverdueAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行利息\">\r\n              <el-input v-model=\"trialForminterest\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"代偿总金额\">\r\n              <el-input v-model=\"trialFormall\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"风险金划比例\" prop=\"fxjProportion\">\r\n              <el-input v-model=\"trialForm.fxjProportion\" @input=\"fxjCount\">\r\n                <template #append>%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"风险金划金额\" prop=\"fxjMoney\">\r\n              <el-input v-model=\"trialForm.fxjMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"fxjAccount\">\r\n              <el-select v-model=\"trialForm.fxjAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"渠道转入比例\" prop=\"qdProportion\">\r\n              <el-input v-model=\"trialForm.qdProportion\" @input=\"qdCount\">\r\n                <template #append>%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"渠道转入金额\" prop=\"qdMoney\">\r\n              <el-input v-model=\"trialForm.qdMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"qdAccount\">\r\n              <el-select v-model=\"trialForm.qdAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"广明借比例\" prop=\"gmjProportion\">\r\n              <el-input v-model=\"trialForm.gmjProportion\" @input=\"gmjCount\">\r\n                <template #append>%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"广明借额\" prop=\"gmjMoney\">\r\n              <el-input v-model=\"trialForm.gmjMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"gmjAccount\">\r\n              <el-select v-model=\"trialForm.gmjAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技借比例\" prop=\"kjczProportion\">\r\n              <el-input v-model=\"trialForm.kjczProportion\" @input=\"kjjCount\">\r\n                <template #append>%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技借额\" prop=\"kjjMoney\">\r\n              <el-input v-model=\"trialForm.kjjMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"kjjAccount\">\r\n              <el-select v-model=\"trialForm.kjjAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技出资比例\" prop=\"kjczProportion\">\r\n              <el-input v-model=\"trialForm.kjczProportion\" @input=\"kjczCount\">\r\n                <template #append>%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技出资金额\" prop=\"kjczMoney\">\r\n              <el-input v-model=\"trialForm.kjczMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"kjczAccount\">\r\n              <el-select v-model=\"trialForm.kjczAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"守邦出资比例\" prop=\"sbczProportion\">\r\n              <el-input v-model=\"trialForm.sbczProportion\" @input=\"sbczCount\">\r\n                <template #append>%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"守邦出资金额\" prop=\"sbczMoney\">\r\n              <el-input v-model=\"trialForm.sbczMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"sbczAccount\">\r\n              <el-select v-model=\"trialForm.sbczAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"代扣剩余未还金额\">\r\n              <el-input v-model=\"trialFormdoverdueAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"违约金\">\r\n              <el-input v-model=\"trialFormliquidatedDamages\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"登记其他欠款金额\" prop=\"otherDebt\">\r\n              <el-input type=\"number\" @input=\"handleInput3\" v-model=\"trialFormotherDebt\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"总账款\">\r\n              <el-input v-model=\"trialFormtotal\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button v-if=\"trialForm.examineStatus != 1\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel('commuteopen')\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 贷款人信息对话框 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n    <!-- 车辆信息 -->\r\n    <carInfo ref=\"carInfo\" :visible.sync=\"carInfoVisible\" title=\"车辆信息\" :plateNo=\"plateNo\" permission=\"2\" />\r\n\r\n    <!-- 催记日志组件 -->\r\n    <loan-reminder-log ref=\"loanReminderLog\" :loan-id=\"currentRow.loanId\" />\r\n    <DispatchVehicleForm ref=\"dispatchVehicleForm\" :loanId=\"dispatchLoanId\" />\r\n\r\n    <!-- 日常费用申请弹窗 -->\r\n    <el-dialog :title=\"getDailyExpenseDialogTitle()\" :visible.sync=\"dailyExpenseDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"dailyExpenseForm\" :model=\"dailyExpenseForm\" :rules=\"dailyExpenseRules\" label-width=\"120px\">\r\n        <el-form-item label=\"费用类型\" prop=\"expenseType\">\r\n          <el-select v-model=\"dailyExpenseForm.expenseType\" placeholder=\"请选择费用类型\" style=\"width: 100%\">\r\n            <el-option label=\"油费\" value=\"oil_fee\" />\r\n            <el-option label=\"路费\" value=\"road_fee\" />\r\n            <el-option label=\"餐费\" value=\"meal_fee\" />\r\n            <el-option label=\"住宿费\" value=\"accommodation_fee\" />\r\n            <el-option label=\"交通费\" value=\"transport_fee\" />\r\n            <el-option label=\"停车费\" value=\"parking_fee\" />\r\n            <el-option label=\"通讯费\" value=\"communication_fee\" />\r\n            <el-option label=\"其他\" value=\"other\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"费用金额\" prop=\"expenseAmount\">\r\n          <el-input v-model=\"dailyExpenseForm.expenseAmount\" placeholder=\"请输入费用金额\">\r\n            <template slot=\"prepend\">￥</template>\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"费用发生日期\" prop=\"expenseDate\">\r\n          <el-date-picker\r\n            v-model=\"dailyExpenseForm.expenseDate\"\r\n            type=\"date\"\r\n            placeholder=\"请选择费用发生日期\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            style=\"width: 100%\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"费用说明\" prop=\"expenseDescription\">\r\n          <el-input\r\n            v-model=\"dailyExpenseForm.expenseDescription\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入费用说明\">\r\n          </el-input>\r\n        </el-form-item>\r\n\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitDailyExpense\">确 定</el-button>\r\n        <el-button @click=\"cancelDailyExpense\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listVw_account_loan } from '@/api/petition_view/petition_view'\r\nimport { listCar_team } from '@/api/car_team/car_team'\r\nimport { addCar_order } from '@/api/car_order/car_order'\r\nimport { Message } from 'element-ui'\r\nimport { get_bank_account, dc_submit_order, loan_compensation_order } from '@/api/vw_account_loan/vw_account_loan'\r\nimport { addDaily_expense_approval } from '@/api/daily_expense_approval/daily_expense_approval'\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport carInfo from '@/layout/components/Dialog/carInfo.vue'\r\nimport LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'\r\nimport DispatchVehicleForm from '@/layout/components/Dialog/dispatchVehicleForm.vue'\r\n\r\nexport default {\r\n  name: 'Vw_account_loan',\r\n  components: {\r\n    userInfo,\r\n    carInfo,\r\n    LoanReminderLog,\r\n    DispatchVehicleForm,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // VIEW表格数据\r\n      vw_account_loanList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否弹出发起代偿对话框\r\n      commuteopen: false,\r\n      trialForm: {},\r\n      trialFormprincipal: 0,\r\n      trialFormboverdueAmount: 0,\r\n      trialForminterest: 0,\r\n      trialFormall: 0,\r\n      trialFormdoverdueAmount: 0,\r\n      trialFormliquidatedDamages: 0,\r\n      trialFormtotal: 0,\r\n      trialFormotherDebt: 0,\r\n      accountList: [], //银行账户列表\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 15,\r\n        customerName: null,\r\n        applyNo: null, // 更新为后端字段\r\n        plateNo: null,\r\n        salesman: null,\r\n        jgName: null,\r\n        partnerId: null,\r\n        petitionName: null,\r\n        followStatus: null,\r\n        dispatchStatus: null,\r\n        allocationTime: null,\r\n        startTime: null,\r\n        endTime: null,\r\n        slippageStatus: 3,\r\n        teamId: null,\r\n        garageId: null,\r\n        libraryStatus: null,\r\n        inboundTime: null,\r\n        outboundTime: null,\r\n        locatingCommission: null,\r\n        GPS: null,\r\n        keyStatus: null,\r\n        keyTime: null,\r\n        collectionMethod: null,\r\n        status: null,\r\n        keyProvince: null,\r\n        keyCity: null,\r\n        keyBorough: null,\r\n        keyAddress: null,\r\n        keyDetailAddress: null,\r\n        carStatus: null,\r\n        isFindCar: null,\r\n      },\r\n      bankList: [\r\n        { value: 'EO00000010', label: '苏银金租' },\r\n        { value: 'IO00000006', label: '浙商银行' },\r\n        { value: 'IO00000007', label: '中关村银行' },\r\n        { value: 'IO00000008', label: '蓝海银行' },\r\n        { value: 'IO00000009', label: '华瑞银行' },\r\n        { value: 'IO00000010', label: '皖新租赁' },\r\n      ],\r\n      followUpList: [\r\n        { label: '无法跟进', value: 1 },\r\n        { label: '约定还款', value: 2 },\r\n        { label: '继续联系', value: 3 },\r\n      ],\r\n      dispatchList: [\r\n        { label: '待派单', value: 1 },\r\n        { label: '找车中', value: 2 },\r\n        { label: '已入库', value: 3 },\r\n        { label: '未派单', value: 4 },\r\n        { label: '已撤销', value: 5 },\r\n      ],\r\n      carStatusList: [\r\n        { label: '省内正常行驶', value: '1' },\r\n        { label: '省外正常行驶', value: '2' },\r\n        { label: '抵押', value: '3' },\r\n        { label: '疑似抵押', value: '4' },\r\n        { label: '疑似黑车', value: '5' },\r\n        { label: '已入库', value: '6' },\r\n        { label: '车在法院', value: '7' },\r\n        { label: '已法拍', value: '8' },\r\n        { label: '协商卖车', value: '9' },\r\n      ],\r\n      // 表单参数\r\n      form: {\r\n        customerName: '',\r\n        mobilePhone: '',\r\n        contractId: '',\r\n        customerId: '',\r\n        plateNo: '',\r\n        carStatus: '',\r\n        carDetailAddress: '',\r\n        gpsStatus: '',\r\n        followUpType: '',\r\n        applyNo: '',\r\n        teamId: null,\r\n        garageId: null,\r\n        libraryStatus: null,\r\n        inboundTime: '',\r\n        outboundTime: '',\r\n        locatingCommission: null,\r\n        keyStatus: null,\r\n        keyTime: '',\r\n        collectionMethod: null,\r\n        status: null,\r\n        keyProvince: '',\r\n        keyCity: '',\r\n        keyBorough: '',\r\n        keyAddress: '',\r\n        keyDetailAddress: '',\r\n      },\r\n      // 表单校验规则\r\n      rules: {},\r\n      radio: 0,\r\n      // 添加新的数据属性\r\n      customerInfo: { customerId: '', applyId: '' },\r\n      userInfoVisible: false,\r\n      plateNo: '',\r\n      carInfoVisible: false,\r\n      currentRow: {},\r\n      dispatchLoanId: null,\r\n      // 日常费用申请相关\r\n      dailyExpenseDialogVisible: false,\r\n      currentLoanRow: {},\r\n      dailyExpenseForm: {\r\n        litigationCaseId: null,\r\n        loanId: null,\r\n        status: 2, // 上访提交\r\n        expenseType: '',\r\n        expenseAmount: '',\r\n        expenseDate: '',\r\n        expenseDescription: '',\r\n        receiptUrl: '',\r\n        applicantId: '',\r\n        applicantName: '',\r\n        applicationTime: '',\r\n        approvalStatus: '0'\r\n      },\r\n      dailyExpenseRules: {\r\n        expenseType: [{ required: true, message: '请选择费用类型', trigger: 'change' }],\r\n        expenseAmount: [\r\n          { required: true, message: '请输入费用金额', trigger: 'blur' },\r\n          { pattern: /^\\d+(\\.\\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }\r\n        ],\r\n        expenseDate: [{ required: true, message: '请选择费用发生日期', trigger: 'change' }],\r\n        expenseDescription: [{ required: true, message: '请输入费用说明', trigger: 'blur' }]\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getCarTeam()\r\n    this.getBankList()\r\n  },\r\n  methods: {\r\n    /** 查询VIEW列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listVw_account_loan(this.queryParams)\r\n        .then(response => {\r\n          this.vw_account_loanList = response.rows\r\n          this.total = response.total\r\n          this.loading = false\r\n        })\r\n        .catch(() => {\r\n          this.loading = false\r\n        })\r\n    },\r\n    fxjCount(val) {\r\n      this.trialForm.fxjMoney = Number(Number(val) * this.trialFormall) / 100\r\n    },\r\n    qdCount(val) {\r\n      this.trialForm.qdMoney = Number(Number(val) * this.trialFormall) / 100\r\n    },\r\n    gmjCount(val) {\r\n      this.trialForm.gmjMoney = Number(Number(val) * this.trialFormall) / 100\r\n    },\r\n    kjjCount(val) {\r\n      this.trialForm.kjjMoney = Number(Number(val) * this.trialFormall) / 100\r\n    },\r\n    kjczCount(val) {\r\n      this.trialForm.kjczMoney = Number(Number(val) * this.trialFormall) / 100\r\n    },\r\n    sbczCount(val) {\r\n      this.trialForm.sbczMoney = Number(Number(val) * this.trialFormall) / 100\r\n    },\r\n    handleInput3(value) {\r\n      this.trialFormotherDebt = Number(value)\r\n      this.trialFormtotal = Number(\r\n        this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt\r\n      ).toFixed(2)\r\n    },\r\n    trialSub() {\r\n      var data = {\r\n        applyId: this.trialForm.applyId,\r\n        id: this.trialForm.id,\r\n        loanId: this.trialForm.loanId,\r\n        loanAmount: this.trialForm.loanAmount,\r\n        partnerId: this.trialForm.partnerId,\r\n      }\r\n      dc_submit_order(data).then(response => {\r\n        this.trialFormprincipal = response.data.principal || 0\r\n        this.trialForm.defaultInterest = response.data.defaultInterest || 0\r\n        this.trialForminterest = response.data.interest || 0\r\n        this.trialFormall = response.data.btotalMoney || 0\r\n        this.trialFormdoverdueAmount = response.data.dtotalMoney || 0\r\n        this.trialFormliquidatedDamages = response.data.liquidatedDamages || 0\r\n\r\n        // this.trialFormboverdueAmount = Number(\r\n        //   this.trialFormprincipal +\r\n        //     this.trialForminterest +\r\n        //     this.trialForm.defaultInterest\r\n        // ).toFixed(2);\r\n        // this.trialFormboverdueAmount = this.trialFormboverdueAmount\r\n        this.trialFormtotal = Number(\r\n          this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt\r\n        ).toFixed(2)\r\n      })\r\n    },\r\n    getBankList() {\r\n      get_bank_account().then(response => {\r\n        this.accountList = response.rows\r\n      })\r\n    },\r\n    getCarTeam() {\r\n      this.loading = true\r\n      listCar_team({ status: 1 })\r\n        .then(response => {\r\n          this.car_team = response.rows\r\n          this.loading = false\r\n        })\r\n        .catch(() => {\r\n          this.loading = false\r\n        })\r\n    },\r\n    // 取消按钮\r\n    cancel(dialog) {\r\n      if (dialog === 'commuteopen') {\r\n        this.commuteopen = false\r\n      }\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.$refs.form?.resetFields() // 重置表单验证\r\n      this.form = {}\r\n      this.queryParams.carStatus = null\r\n      this.queryParams.isFindCar = null\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      if (this.queryParams.allocationTime) {\r\n        this.queryParams.startTime = this.queryParams.allocationTime[0]\r\n        this.queryParams.endTime = this.queryParams.allocationTime[1]\r\n      }\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams.customerName = null\r\n      this.queryParams.certId = null\r\n      this.queryParams.plateNo = null\r\n      this.queryParams.salesman = null\r\n      this.queryParams.jgName = null\r\n      this.queryParams.partnerId = null\r\n      this.queryParams.petitionName = null\r\n      this.queryParams.followStatus = null\r\n      this.queryParams.dispatchStatus = null\r\n      this.queryParams.allocationTime = null\r\n      this.queryParams.startTime = null\r\n      this.queryParams.endTime = null\r\n      this.queryParams.carStatus = null\r\n      this.queryParams.isFindCar = null\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      // This method is no longer needed as selection is removed.\r\n      // Keeping it for now as it might be used elsewhere or for future changes.\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.open = true\r\n      this.title = '修改VIEW'\r\n    },\r\n    /** 发起代偿操作 */\r\n    initiate(row) {\r\n      //发起代偿\r\n      var data = {\r\n        loanId: row.loanId,\r\n      }\r\n      loan_compensation_order(data).then(response => {\r\n        if (response.data) {\r\n          this.trialForm = response.data\r\n          this.trialForm.loanAmount = row.contractAmt || 0\r\n          this.trialForm.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n          this.trialFormprincipal = response.data.trialBalance ? response.data.trialBalance.principal : 0\r\n          this.trialFormboverdueAmount = row.boverdueAmount || 0\r\n          this.trialForminterest = response.data.trialBalance ? response.data.trialBalance.interest : 0\r\n          this.trialFormall = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0\r\n          this.trialFormdoverdueAmount = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n          this.trialFormliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0\r\n          this.trialFormotherDebt = response.data.otherDebt ? response.data.otherDebt : 0\r\n          console.log(this.trialFormall, this.trialFormdoverdueAmount, this.trialFormliquidatedDamages, this.trialFormotherDebt)\r\n          this.trialFormtotal = Number(\r\n            this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt\r\n          ).toFixed(2)\r\n        } else {\r\n          this.addTrial(row)\r\n        }\r\n        this.commuteopen = true\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          // 调用后端接口添加找车订单\r\n          addCar_order(this.form)\r\n            .then(response => {\r\n              if (response.code === 200) {\r\n                Message({\r\n                  message: '找车订单添加成功',\r\n                  type: 'success',\r\n                  duration: 3 * 1000,\r\n                })\r\n                this.getList() // 刷新列表\r\n                this.reset() // 重置表单\r\n              } else {\r\n                Message({\r\n                  message: '添加失败: ' + response.message,\r\n                  type: 'error',\r\n                  duration: 3 * 1000,\r\n                })\r\n              }\r\n            })\r\n            .catch(error => {\r\n              Message({\r\n                message: '服务器错误: ' + error.message,\r\n                type: 'error',\r\n                duration: 3 * 1000,\r\n              })\r\n            })\r\n        } else {\r\n          console.log('表单验证失败')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 添加新的方法\r\n    openUserInfo(customerInfo) {\r\n      this.customerInfo = customerInfo\r\n      this.userInfoVisible = true\r\n    },\r\n    openCarInfo(plateNo) {\r\n      this.plateNo = plateNo\r\n      this.carInfoVisible = true\r\n    },\r\n    // 查看催记日志\r\n    logView(row) {\r\n      this.currentRow = row\r\n      this.$refs.loanReminderLog.openLogDialog()\r\n    },\r\n    openDispatchVehicleForm(row) {\r\n      this.dispatchLoanId = row.loanId\r\n      this.$refs.dispatchVehicleForm.openDialog()\r\n    },\r\n    // 打开日常费用申请弹窗\r\n    openDailyExpenseDialog(row) {\r\n      this.resetDailyExpenseForm()\r\n      this.dailyExpenseForm.applicantId = String(this.$store.state.user?.id || '')\r\n      this.dailyExpenseForm.applicantName = String(this.$store.state.user?.name || '')\r\n      // 设置当前日期作为申请时间\r\n      const today = new Date()\r\n      this.dailyExpenseForm.applicationTime = today.toISOString().split('T')[0]\r\n      // 设置贷款ID和状态（上访提交）\r\n      this.dailyExpenseForm.loanId = row.loanId\r\n      this.dailyExpenseForm.status = 2 // 上访提交\r\n      this.currentLoanRow = row\r\n      this.dailyExpenseDialogVisible = true\r\n    },\r\n    // 重置日常费用表单\r\n    resetDailyExpenseForm() {\r\n      this.dailyExpenseForm = {\r\n        litigationCaseId: null,\r\n        loanId: null,\r\n        status: 2, // 上访提交\r\n        expenseType: '',\r\n        expenseAmount: '',\r\n        expenseDate: '',\r\n        expenseDescription: '',\r\n        receiptUrl: '',\r\n        applicantId: '',\r\n        applicantName: '',\r\n        applicationTime: '',\r\n        approvalStatus: '0'\r\n      }\r\n      this.currentLoanRow = {}\r\n      this.$refs.dailyExpenseForm?.resetFields()\r\n    },\r\n    // 取消日常费用申请\r\n    cancelDailyExpense() {\r\n      this.dailyExpenseDialogVisible = false\r\n      this.resetDailyExpenseForm()\r\n    },\r\n    // 提交日常费用申请\r\n    submitDailyExpense() {\r\n      this.$refs.dailyExpenseForm.validate((valid) => {\r\n        if (valid) {\r\n          // 确保 litigationCaseId 有值，如果没有则设置为 0\r\n          const submitData = { ...this.dailyExpenseForm }\r\n          if (!submitData.litigationCaseId) {\r\n            submitData.litigationCaseId = 0\r\n          }\r\n          addDaily_expense_approval(submitData).then(res => {\r\n            if (res.code === 200) {\r\n              this.$message.success('日常费用申请提交成功')\r\n              this.dailyExpenseDialogVisible = false\r\n              this.resetDailyExpenseForm()\r\n            } else {\r\n              this.$message.error('提交失败：' + (res.msg || '未知错误'))\r\n            }\r\n          }).catch(error => {\r\n            console.error('提交失败:', error)\r\n            this.$message.error('提交失败，请稍后重试')\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 获取日常费用弹窗标题\r\n    getDailyExpenseDialogTitle() {\r\n      if (this.currentLoanRow && this.currentLoanRow.customerName) {\r\n        return `提交日常费用 - ${this.currentLoanRow.customerName}`\r\n      }\r\n      return '提交日常费用'\r\n    }\r\n  },\r\n}\r\n</script>\r\n<style>\r\n.dialogBox .el-form-item__label {\r\n  width: 100px !important;\r\n}\r\n\r\n.dialogBox .el-form-item__content {\r\n  margin-left: 100px !important;\r\n}\r\n\r\n.warnBox {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.warnImg {\r\n  width: 20px;\r\n  height: 20px;\r\n  margin: 0 5px 0 10px;\r\n}\r\n\r\n.settle_money {\r\n  background-color: #1890ff;\r\n  color: #fff;\r\n  border-radius: 5px;\r\n  display: inline-block;\r\n  padding: 5px 10px;\r\n  box-sizing: border-box;\r\n  margin-bottom: 10px;\r\n}\r\n/* 操作按钮容器 */\r\n.operation-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n  width: 90px;\r\n  padding: 4px 8px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.operation-btn {\r\n  width: 74px !important;\r\n  height: 26px !important;\r\n  margin: 0 !important;\r\n  padding: 0 2px !important;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  text-align: center;\r\n  line-height: 26px;\r\n}\r\n\r\n/* 按钮悬停效果 */\r\n.operation-btn:hover {\r\n  background-color: #f5f7fa;\r\n  color: #409eff;\r\n}\r\n</style>\r\n<style>\r\n.custom-popover {\r\n  width: 116px !important;\r\n  min-width: 116px !important;\r\n  max-width: 116px !important;\r\n  box-sizing: border-box !important;\r\n}\r\n</style>\r\n"]}]}