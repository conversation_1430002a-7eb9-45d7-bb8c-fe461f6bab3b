package com.ruoyi.web.controller.wx;


import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.mapper.WxUserMapper;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.controller.BaseController;

import java.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/api/wx")
public class WxLoginController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(WxLoginController.class);

    @Autowired
    private WxMaService wxMaService;

    @Autowired
    private WxUserMapper wxUserMapper;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private SysPermissionService permissionService;


    /**
     * 微信登录 - 使用SDK获取用户openid、手机号、昵称、头像
     */
    @Anonymous
    @PostMapping("/WxMiniLogin")
    public Map<String, Object> WxMiniLogin(@RequestBody Map<String, String> payload) {
        return wxLogin(payload);
    }

    /**
     * 微信登录 - 兼容客户端请求路径
     */
    @Anonymous
    @PostMapping("/wxLogin")
    public Map<String, Object> wxLogin(@RequestBody Map<String, String> payload) {
        try {
            String code = payload.get("code");
            String encryptedData = payload.get("encryptedData");
            String iv = payload.get("iv");

            if (code == null || code.trim().isEmpty()) {
                return error("登录code不能为空");
            }

            // 1. 使用SDK获取session信息（openid和session_key）
            WxMaJscode2SessionResult sessionResult = wxMaService.getUserService().getSessionInfo(code);
            String openid = sessionResult.getOpenid();
            String sessionKey = sessionResult.getSessionKey();

            logger.info("获取到用户openid: {}", openid);

            // 2. 解密手机号（如果提供了加密数据）
            String phoneNumber = null;
            if (encryptedData != null && !encryptedData.trim().isEmpty() &&
                iv != null && !iv.trim().isEmpty()) {
                try {
                    WxMaPhoneNumberInfo phoneInfo = wxMaService.getUserService()
                            .getPhoneNoInfo(sessionKey, encryptedData, iv);
                    phoneNumber = phoneInfo.getPhoneNumber();
                    logger.info("成功解密手机号: {}", phoneNumber);
                } catch (Exception e) {
                    logger.warn("解密手机号失败: {}", e.getMessage());
                    // 手机号解密失败不影响登录流程
                }
            }

            // 3. 业务逻辑：根据手机号查询用户
            Map<String, Object> result = new HashMap<>();
            result.put("openid", openid);
            result.put("phoneNumber", phoneNumber);

            if (phoneNumber != null && !phoneNumber.trim().isEmpty()) {
                // 有手机号，根据手机号查询用户
                SysUser user = sysUserService.selectUserByPhoneNumber(phoneNumber);

                if (user != null) {
                    // 用户存在，绑定openid并返回token
                    try {
                        // 绑定或更新openid
                        if (!openid.equals(user.getOpenid())) {
                            sysUserService.updateUserOpenid(user.getUserName(), openid);
                            logger.info("为用户 {} 绑定openid: {}", user.getUserName(), openid);
                        }

                        // 检查用户角色
                        String userRole = sysUserService.selectUserRoleGroup(user.getUserName());
                        Set<String> allowedRoles = Set.of("出单员工", "上访员工", "强制上访会员", "找车员");

                        if (allowedRoles.contains(userRole)) {
                            // 生成token
                            String token = sysLoginService.Wxlogin(user);

                            // 验证生成的token格式
                            if (token != null && !token.trim().isEmpty()) {
                                result.put("code", 200);
                                result.put("msg", "登录成功");
                                result.put("token", token);
                                result.put("user", userRole);
                                result.put("loginSuccess", true);
                                result.put("isNewUser", false);

                                logger.info("用户 {} 微信登录成功，角色: {}, token长度: {}", user.getUserName(), userRole, token.length());
                            } else {
                                result.put("code", 500);
                                result.put("msg", "Token生成失败");
                                result.put("loginSuccess", false);
                                logger.error("用户 {} Token生成失败", user.getUserName());
                            }
                        } else {
                            result.put("code", 403);
                            result.put("msg", "用户角色异常，无法登录");
                            result.put("loginSuccess", false);
                            logger.warn("用户 {} 角色异常: {}", user.getUserName(), userRole);
                        }

                    } catch (Exception e) {
                        logger.error("用户登录处理失败: {}", e.getMessage(), e);
                        result.put("code", 500);
                        result.put("msg", "登录处理失败");
                        result.put("loginSuccess", false);
                    }

                } else {
                    // 用户不存在，提示不能使用
                    result.put("code", 404);
                    result.put("msg", "该手机号未注册，无法使用此功能");
                    result.put("loginSuccess", false);
                    result.put("isNewUser", true);
                    logger.info("手机号 {} 未找到对应用户", phoneNumber);
                }

            } else {
                // 没有手机号，提示需要授权手机号
                result.put("code", 400);
                result.put("msg", "请先授权手机号");
                result.put("loginSuccess", false);
                result.put("needPhoneAuth", true);
                logger.info("用户未授权手机号，openid: {}", openid);
            }

            return result;

        } catch (Exception e) {
            logger.error("微信登录异常: {}", e.getMessage(), e);
            return error("微信登录失败: " + e.getMessage());
        }
    }



}