package com.ruoyi.web.controller.wx;

import com.ruoyi.coborrower_info.domain.CoborrowerInfo;
import com.ruoyi.coborrower_info.service.ICoborrowerInfoService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.customer_relative.domain.CustomerRelative;
import com.ruoyi.customer_relative.service.ICustomerRelativeService;
import com.ruoyi.ind_work.domain.IndWork;
import com.ruoyi.ind_work.service.IIndWorkService;
import com.ruoyi.system.service.impl.SysUserServiceImpl;
import com.ruoyi.vw_car.domain.VwCar;
import com.ruoyi.vw_car.service.IVwCarService;
import com.ruoyi.vw_customer_info.domain.VwCustomerInfo;
import com.ruoyi.vw_customer_info.service.IVwCustomerInfoService;
import com.ruoyi.vw_loan_info.domain.vw_loan_info;
import com.ruoyi.vw_loan_info.service.Ivw_loan_infoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * VIEWController
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@RestController
@RequestMapping("/app/loan_info")
public class wx_loan_infoController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(wx_loan_infoController.class);

    @Autowired
    private Ivw_loan_infoService vw_loan_infoService;

    @Autowired
    private SysUserServiceImpl sysUserService;

    @Autowired
    private IVwCustomerInfoService vwCustomerInfoService;

    @Autowired
    private ICoborrowerInfoService coborrowerInfoService;

    @Autowired
    private ICustomerRelativeService customerRelativeService;

    @Autowired
    private IVwCarService vwCarService;

    @Autowired
    private IIndWorkService indWorkService;


    /**
     * 查询VIEW列表
     */
//    @PreAuthorize("@ss.hasPermi('vw_loan_info:vw_loan_info:list')")
    @GetMapping("/list")
    @Anonymous
    public TableDataInfo list(vw_loan_info vw_loan_info)
    {
        startPage();

        try {
            // 尝试获取用户信息，如果失败则跳过权限检查
            String username = getUsername();
            //查询用户权限
            String userRole = sysUserService.selectUserRoleGroup(username);
            if (Objects.equals(userRole, "出单员工")) {
                if (vw_loan_info.getType()==0) {
                    vw_loan_info.setSlippageStatus("1");
                }else if (vw_loan_info.getType()==1) {
                    vw_loan_info.setSlippageStatus("1");
                    vw_loan_info.setIsExtension("0");
                    vw_loan_info.setStatus("2,3");
                }else if (vw_loan_info.getType()==2) {
                    vw_loan_info.setSlippageStatus("1");
                    vw_loan_info.setIsExtension("1");
                    vw_loan_info.setStatus("2,3");
                }else if (vw_loan_info.getType()==3) {
                    vw_loan_info.setSlippageStatus("1");
                    vw_loan_info.setStatus("1");
                }
            }
        } catch (Exception e) {
            // 匿名访问时，无法获取用户信息，跳过权限检查
            logger.debug("匿名访问，跳过用户权限检查: {}", e.getMessage());
        }

        List<vw_loan_info> list = vw_loan_infoService.selectvw_loan_infoList(vw_loan_info);
        return getDataTable(list);
    }

    @GetMapping("/detail")
    @Anonymous
    public Map<String , Object>list( @RequestParam Map<String, String> payload)
    {
//        String customerId = payload.get("customerId");
//        String applyId = payload.get("applyId");
        String id = payload.get("id");

        //查询逾期信息
        vw_loan_info vw_loan_info = vw_loan_infoService.selectvw_loan_infoById(id);

        if (vw_loan_info == null) {
            return error("未找到对应客户信息");
        }

        // 查询主信息
        VwCustomerInfo vwCustomerInfo = vwCustomerInfoService.selectVwCustomerInfoById(vw_loan_info.getCustomerId());
        if (vwCustomerInfo == null) {
            return error("未找到对应客户信息");
        }

        //查看客户工作信息
        IndWork work = new IndWork();
        work.setCustomerId(vw_loan_info.getCustomerId());
        List<IndWork> workList = indWorkService.selectIndWorkList(work);

        // 构造查询条件（假设根据 applyNo 查询共担保人）
        CoborrowerInfo query1 = new CoborrowerInfo();
        query1.setApplyNo(vw_loan_info.getApplyId()); // 根据实际字段名调整

        // 查询共担保人列表
        List<CoborrowerInfo> coborrowerInfoList = coborrowerInfoService.selectCoborrowerInfoList(query1);

        CustomerRelative query2 = new CustomerRelative();
        query2.setApplyNo(vw_loan_info.getApplyId()); // 根据实际字段名调整
        //查询联系人列表
        List<CustomerRelative> customerRelative = customerRelativeService.selectCustomerRelativeList(query2);


        //查询车辆信息
        VwCar query3 = new VwCar();
        query3.setApplyNo(vw_loan_info.getApplyId());
        List<VwCar> vwCar = vwCarService.selectVwCarList(query3);

        //查询逾期信息





        // 构造返回结果（可以使用 Map 或自定义 VO）
        Map<String, Object> result = new HashMap<>();
        result.put("customerInfo", vwCustomerInfo);//客户信息
        result.put("coborrowerList", coborrowerInfoList);//担保人信息
        result.put("customerRelative", customerRelative);//联系人信息
        result.put("carList", vwCar); //车辆信息
        result.put("loanInfo", vw_loan_info); //逾期信息
        result.put("workList", workList);//工作信息

        return result;
    }
}
