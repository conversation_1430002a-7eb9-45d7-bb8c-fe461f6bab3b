package com.ruoyi.car_order_examine.utils;

import com.ruoyi.car_order_examine.domain.CarOrderExamine;

/**
 * 审批流程状态管理工具类
 * 
 * 审批状态定义：
 * 0: 未审批
 * 1: 全部同意（最终完成状态）
 * 2: 已拒绝（最终拒绝状态）
 * 3: 法诉主管审批（当前审批节点）
 * 4: 总监审批（当前审批节点）
 * 5: 财务主管/总监抄送（当前审批节点）
 * 6: 总经理/董事长审批(抄送)（当前审批节点）
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public class ApprovalStatusManager {

    // 审批流程顺序
    private static final int[] APPROVAL_FLOW = {
        CarOrderExamine.STATUS_LEGAL_SUPERVISOR,   // 法诉主管审批
        CarOrderExamine.STATUS_DIRECTOR,           // 总监审批
        CarOrderExamine.STATUS_DIRECTOR_CC,        // 财务主管/总监抄送
        CarOrderExamine.STATUS_GENERAL_MANAGER     // 总经理/董事长审批
    };

    /**
     * 获取下一个审批状态
     * @param currentStatus 当前状态
     * @return 下一个状态，如果没有下一个状态返回null
     */
    public static Integer getNextStatus(Integer currentStatus) {
        if (currentStatus == null) {
            return null;
        }

        for (int i = 0; i < APPROVAL_FLOW.length; i++) {
            if (APPROVAL_FLOW[i] == currentStatus) {
                if (i == APPROVAL_FLOW.length - 1) {
                    // 最后一个节点，返回全部同意
                    return CarOrderExamine.STATUS_APPROVED;
                } else {
                    // 返回下一个节点
                    return APPROVAL_FLOW[i + 1];
                }
            }
        }
        return null;
    }

    /**
     * 检查是否为最后一个审批节点
     * @param status 当前状态
     * @return 是否为最后一个审批节点
     */
    public static boolean isLastApprovalNode(Integer status) {
        return status != null && status == CarOrderExamine.STATUS_GENERAL_MANAGER;
    }

    /**
     * 检查是否为最终状态（已完成或已拒绝）
     * @param status 状态
     * @return 是否为最终状态
     */
    public static boolean isFinalStatus(Integer status) {
        return status != null && 
               (status == CarOrderExamine.STATUS_APPROVED || status == CarOrderExamine.STATUS_REJECTED);
    }

    /**
     * 检查是否可以进行审批操作
     * @param status 当前状态
     * @return 是否可以审批
     */
    public static boolean canApprove(Integer status) {
        if (status == null) {
            return false;
        }
        
        for (int approvalStatus : APPROVAL_FLOW) {
            if (approvalStatus == status) {
                return true;
            }
        }
        return false;
    }

    /**
     * 处理审批通过
     * @param currentStatus 当前状态
     * @return 新状态
     * @throws IllegalStateException 当前状态不允许审批操作
     */
    public static Integer handleApprove(Integer currentStatus) {
        if (!canApprove(currentStatus)) {
            throw new IllegalStateException("当前状态不允许审批操作");
        }

        // 如果是最后一个审批节点，设置为全部同意
        if (isLastApprovalNode(currentStatus)) {
            return CarOrderExamine.STATUS_APPROVED;
        }

        // 否则进入下一个审批节点
        Integer nextStatus = getNextStatus(currentStatus);
        if (nextStatus == null) {
            throw new IllegalStateException("无法获取下一个审批状态");
        }

        return nextStatus;
    }

    /**
     * 处理审批拒绝
     * @param currentStatus 当前状态
     * @return 新状态（始终为已拒绝）
     * @throws IllegalStateException 当前状态不允许审批操作
     */
    public static Integer handleReject(Integer currentStatus) {
        if (!canApprove(currentStatus)) {
            throw new IllegalStateException("当前状态不允许审批操作");
        }
        return CarOrderExamine.STATUS_REJECTED;
    }

    /**
     * 开始审批流程
     * @return 第一个审批节点状态
     */
    public static Integer startApprovalFlow() {
        return APPROVAL_FLOW[0];
    }

    /**
     * 获取状态描述文本
     * @param status 状态码
     * @return 状态描述
     */
    public static String getStatusText(Integer status) {
        if (status == null) {
            return "未知状态";
        }

        switch (status) {
            case CarOrderExamine.STATUS_PENDING:
                return "未审批";
            case CarOrderExamine.STATUS_APPROVED:
                return "全部同意";
            case CarOrderExamine.STATUS_REJECTED:
                return "已拒绝";
            case CarOrderExamine.STATUS_LEGAL_SUPERVISOR:
                return "法诉主管审批";
            case CarOrderExamine.STATUS_DIRECTOR:
                return "总监审批";
            case CarOrderExamine.STATUS_DIRECTOR_CC:
                return "财务主管/总监抄送";
            case CarOrderExamine.STATUS_GENERAL_MANAGER:
                return "总经理/董事长审批";
            default:
                return "未知状态";
        }
    }

    /**
     * 验证状态转换是否合法
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @return 是否合法
     */
    public static boolean isValidStatusTransition(Integer fromStatus, Integer toStatus) {
        if (fromStatus == null || toStatus == null) {
            return false;
        }

        // 已完成或已拒绝的状态不能再转换
        if (isFinalStatus(fromStatus)) {
            return false;
        }

        // 只能转换为拒绝状态或下一个审批节点
        if (toStatus == CarOrderExamine.STATUS_REJECTED) {
            return canApprove(fromStatus);
        }

        // 检查是否为合法的下一个状态
        if (isLastApprovalNode(fromStatus)) {
            return toStatus == CarOrderExamine.STATUS_APPROVED;
        }

        return toStatus.equals(getNextStatus(fromStatus));
    }

    /**
     * 根据用户角色检查是否可以审批当前状态
     * @param status 当前状态
     * @param userRole 用户角色
     * @return 是否可以审批
     */
    public static boolean canUserApprove(Integer status, String userRole) {
        if (status == null || userRole == null) {
            return false;
        }

        switch (status) {
            case CarOrderExamine.STATUS_PENDING:
                return "法诉主管".equals(userRole);
            case CarOrderExamine.STATUS_LEGAL_SUPERVISOR:
                return "法诉主管".equals(userRole);
            case CarOrderExamine.STATUS_DIRECTOR:
                return "总监".equals(userRole);
            case CarOrderExamine.STATUS_DIRECTOR_CC:
                return "财务主管".equals(userRole) || "财务总监".equals(userRole);
            case CarOrderExamine.STATUS_GENERAL_MANAGER:
                return "总经理".equals(userRole) || "董事长".equals(userRole);
            default:
                return false;
        }
    }

    /**
     * 获取当前状态需要的审批角色
     * @param status 当前状态
     * @return 需要的审批角色
     */
    public static String getRequiredRole(Integer status) {
        if (status == null) {
            return "未知";
        }

        switch (status) {
            case CarOrderExamine.STATUS_PENDING:
                return "法诉主管";
            case CarOrderExamine.STATUS_LEGAL_SUPERVISOR:
                return "法诉主管";
            case CarOrderExamine.STATUS_DIRECTOR:
                return "总监";
            case CarOrderExamine.STATUS_DIRECTOR_CC:
                return "财务主管/总监";
            case CarOrderExamine.STATUS_GENERAL_MANAGER:
                return "总经理/董事长";
            default:
                return "未知";
        }
    }
}
