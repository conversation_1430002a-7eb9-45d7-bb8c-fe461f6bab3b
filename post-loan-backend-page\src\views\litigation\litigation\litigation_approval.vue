<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- 1. 贷款人姓名 -->
      <el-form-item label="" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="贷款人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- 2. 贷款人身份证号 -->
      <el-form-item label="" prop="certId">
        <el-input
          v-model="queryParams.certId"
          placeholder="贷款人身份证号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- 3. 出单渠道 -->
      <el-form-item label="" prop="jgName">
        <el-input
          v-model="queryParams.jgName"
          placeholder="出单渠道"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- 4. 放款银行 -->
      <el-form-item label="" prop="lendingBank">
        <el-input
          v-model="queryParams.lendingBank"
          placeholder="放款银行"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- 5. 法诉状态(需呈现多级) -->
      <el-form-item label="" prop="litigationStatus">
        <el-cascader
          v-model="queryParams.litigationStatus"
          :options="litigationStatusOptions"
          :props="{ expandTrigger: 'hover', value: 'value', label: 'label', children: 'children' }"
          placeholder="法诉状态"
          clearable
          @change="handleQuery"
        />
      </el-form-item>

      <!-- 6. 申请人 -->
      <el-form-item label="" prop="applicationBy">
        <el-input
          v-model="queryParams.applicationBy"
          placeholder="申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- 7. 费用类型 -->
      <el-form-item label="" prop="costCategory">
        <el-select v-model="queryParams.costCategory" placeholder="费用类型" clearable @change="handleQuery">
          <el-option label="律师费" value="律师费" />
          <el-option label="诉讼费" value="诉讼费" />
          <el-option label="保全费" value="保全费" />
          <el-option label="执行费" value="执行费" />
          <el-option label="其他费用" value="其他费用" />
        </el-select>
      </el-form-item>

      <!-- 8. 审批状态 -->
      <el-form-item label="" prop="approvalStatus">
        <el-select v-model="queryParams.approvalStatus" placeholder="审批状态" clearable @change="handleQuery">
          <el-option label="未审核" value="" />
          <el-option label="已通过" value="0" />
          <el-option label="已拒绝" value="1" />
        </el-select>
      </el-form-item>

      <!-- 9. 申请时间区间 -->
      <el-form-item label="" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="申请开始日期"
          end-placeholder="申请结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          @change="handleDateRangeChange"
        />
      </el-form-item>

      <!-- 10. 审批时间区间 -->
      <el-form-item label="" prop="approvalDateRange">
        <el-date-picker
          v-model="approvalDateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="审批开始日期"
          end-placeholder="审批结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          @change="handleApprovalDateRangeChange"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['vm_car_order:vm_car_order:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleBatchEdit"
          v-hasPermi="['vm_car_order:vm_car_order:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['vm_car_order:vm_car_order:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['vm_car_order:vm_car_order:export']"
        >导出</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="vm_car_orderList" @selection-change="handleSelectionChange" row-key="id" style="width: 100%" flex="right">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" width="60" fixed />
      <el-table-column label="申请人" align="center" prop="applicationBy" width="100" />
      <el-table-column label="最新申请时间" align="center" prop="applicationTime" width="150" />
      <el-table-column label="案件负责人" align="center" prop="curator" width="100" />
      <el-table-column label="提交次数" align="center" prop="submissionCount" width="100" />
      <el-table-column label="法诉状态" align="center" prop="litigationStatus" width="100">
        <template slot-scope="scope">
          <span>{{scope.row.litigationStatus == '1'?'待立案':'已邮寄'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="贷款人" align="center" width="100">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="openUserInfo(scope.row)"
            style="color: #409EFF;">
            {{ scope.row.customerName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="出单渠道" align="center" prop="jgName" />
      <el-table-column label="地区" align="center" prop="area" width="80" />
      <el-table-column label="放款银行" align="center" prop="lendingBank" />
      <el-table-column label="法院地" align="center" prop="courtLocation" />
      <el-table-column label="诉讼法院" align="center" prop="commonPleas" />
      <el-table-column label="律师费" align="center" prop="lawyerFee" width="80" />
      <el-table-column label="诉讼费" align="center" prop="litigationFee" width="80" />
      <el-table-column label="保险费" align="center" prop="insurance" width="80" />
      <el-table-column label="保全费" align="center" prop="preservationFee" width="80" />
      <el-table-column label="布控费" align="center" prop="surveillanceFee" width="80" />
      <el-table-column label="公告费" align="center" prop="announcementFee" width="80" />
      <el-table-column label="评估费" align="center" prop="appraisalFee" width="80" />
      <el-table-column label="执行费" align="center" prop="executionFee" width="80" />
      <el-table-column label="特殊通道费" align="center" prop="specialChannelFees" width="100" />
      <el-table-column label="日常报销" align="center" prop="otherAmountsOwed" width="80" />
      <el-table-column label="总费用" align="center" prop="totalMoney" width="100" />
      <el-table-column label="审批状态" align="center" prop="overallApprovalStatus" width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.overallApprovalStatus == 'NOT_STARTED'" type="info">未开始审批</el-tag>
          <el-tag v-else-if="scope.row.overallApprovalStatus == 'PARTIAL'" type="warning">部分审批</el-tag>
          <el-tag v-else-if="scope.row.overallApprovalStatus == 'COMPLETED'" type="success">全部审批</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审批时间" align="center" prop="approveTime" width="150" />
      <el-table-column label="审批人角色" align="center" prop="approveRole" />
      <el-table-column label="审批人" align="center" prop="approveBy" />
      <el-table-column label="操作" align="center" width="100" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['vm_car_order:vm_car_order:edit']"
          >审批</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 审批对话框 -->
    <el-dialog title="法诉费用审批详情" :visible.sync="open" width="1200px" append-to-body>
      <div class="approval-header">
        <el-row>
          <el-col :span="8">
            <strong>贷款人：</strong>{{ currentRecord.customerName }}
          </el-col>
          <el-col :span="8">
            <strong>案件负责人：</strong>{{ currentRecord.curator }}
          </el-col>
          <el-col :span="8">
            <strong>法院地：</strong>{{ currentRecord.courtLocation }}
          </el-col>
        </el-row>
      </div>

      <div class="batch-approval-section" style="margin: 20px 0;">
        <el-button
          type="success"
          size="small"
          :disabled="selectedRecords.length === 0"
          @click="handleBatchApprove('0')">
          批量通过 ({{ selectedRecords.length }})
        </el-button>
        <el-button
          type="danger"
          size="small"
          :disabled="selectedRecords.length === 0"
          @click="handleBatchApprove('1')">
          批量拒绝 ({{ selectedRecords.length }})
        </el-button>
      </div>

      <el-table
        :data="submissionRecords"
        @selection-change="handleRecordSelectionChange"
        v-loading="recordsLoading">
        <el-table-column type="selection" width="55" align="center" fixed="left" />
        <el-table-column label="提交时间" align="center" prop="applicationTime" width="150" />
        <el-table-column label="提交人" align="center" prop="applicationBy" width="100" />
        <el-table-column label="律师费" align="center" prop="lawyerFee" width="80" />
        <el-table-column label="诉讼费" align="center" prop="litigationFee" width="80" />
        <el-table-column label="保全费" align="center" prop="preservationFee" width="80" />
        <el-table-column label="布控费" align="center" prop="surveillanceFee" width="80" />
        <el-table-column label="公告费" align="center" prop="announcementFee" width="80" />
        <el-table-column label="评估费" align="center" prop="appraisalFee" width="80" />
        <el-table-column label="执行费" align="center" prop="executionFee" width="80" />
        <el-table-column label="违约金" align="center" prop="penalty" width="80" />
        <el-table-column label="担保费" align="center" prop="guaranteeFee" width="80" />
        <el-table-column label="居间费" align="center" prop="intermediaryFee" width="80" />
        <el-table-column label="代偿金" align="center" prop="compensity" width="80" />
        <el-table-column label="判决金额" align="center" prop="judgmentAmount" width="100" />
        <el-table-column label="利息" align="center" prop="interest" width="80" />
        <el-table-column label="其他欠款" align="center" prop="otherAmountsOwed" width="100" />
        <el-table-column label="保险费" align="center" prop="insurance" width="80" />
        <el-table-column label="总费用" align="center" prop="totalMoney" width="100" />
        <el-table-column label="审批状态" align="center" prop="approvalStatus" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.approvalStatus == null || scope.row.approvalStatus == ''" type="info">未审核</el-tag>
            <el-tag v-else-if="scope.row.approvalStatus == '0'" type="success">已通过</el-tag>
            <el-tag v-else-if="scope.row.approvalStatus == '1'" type="danger">已拒绝</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="审批时间" align="center" prop="approveTime" width="150" />
        <el-table-column label="审批人" align="center" prop="approveBy" width="100" />
        <el-table-column label="拒绝原因" align="center" prop="reasons" width="150" />
        <el-table-column label="操作" align="center" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.approvalStatus == null || scope.row.approvalStatus == ''"
              size="mini"
              type="success"
              @click="handleSingleApprove(scope.row, '0')">
              通过
            </el-button>
            <el-button
              v-if="scope.row.approvalStatus == null || scope.row.approvalStatus == ''"
              size="mini"
              type="danger"
              @click="handleSingleApprove(scope.row, '1')">
              拒绝
            </el-button>
            <div v-else>
              <el-tag v-if="scope.row.approvalStatus == '0'" type="success" size="mini">已通过</el-tag>
              <el-tag v-else-if="scope.row.approvalStatus == '1'" type="danger" size="mini">已拒绝</el-tag>
              <div v-if="scope.row.approveBy" style="font-size: 12px; color: #999; margin-top: 2px;">
                {{ scope.row.approveBy }}
              </div>
              <div v-if="scope.row.approveTime" style="font-size: 12px; color: #999;">
                {{ scope.row.approveTime }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 单个审批确认对话框 -->
    <el-dialog title="审批确认" :visible.sync="singleApprovalOpen" width="400px" append-to-body>
      <el-form ref="singleApprovalForm" :model="singleApprovalForm" label-width="80px">
        <el-form-item label="审批结果">
          <el-tag v-if="singleApprovalForm.status == '0'" type="success">通过</el-tag>
          <el-tag v-else type="danger">拒绝</el-tag>
        </el-form-item>
        <el-form-item label="拒绝原因" v-if="singleApprovalForm.status == '1'">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
            v-model="singleApprovalForm.rejectReason">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmSingleApproval">确 定</el-button>
        <el-button @click="singleApprovalOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量审批确认对话框 -->
    <el-dialog title="批量审批确认" :visible.sync="batchApprovalOpen" width="400px" append-to-body>
      <el-form ref="batchApprovalForm" :model="batchApprovalForm" label-width="80px">
        <el-form-item label="审批结果">
          <el-tag v-if="batchApprovalForm.status == '0'" type="success">批量通过</el-tag>
          <el-tag v-else type="danger">批量拒绝</el-tag>
        </el-form-item>
        <el-form-item label="选中记录">
          <span>{{ selectedRecords.length }} 条记录</span>
        </el-form-item>
        <el-form-item label="拒绝原因" v-if="batchApprovalForm.status == '1'">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
            v-model="batchApprovalForm.rejectReason">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmBatchApproval">确 定</el-button>
        <el-button @click="batchApprovalOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 贷款人信息对话框 -->
    <userInfo ref="userInfo" :visible.sync="userInfoVisible" title="贷款人信息" :customerInfo="customerInfo" />
  </div>
</template>

<script>
import {
  listLitigationCostApproval,
  getLitigationCostSubmissionRecords,
  approveLitigationCostRecord,
  batchApproveLitigationCostRecords
} from "@/api/litigation_cost_approval/litigation_cost_approval"
import areaList from "../../../assets/area.json"
import userInfo from '@/layout/components/Dialog/userInfo.vue'
export default {
  name: "Vm_car_order",
  components: {
    userInfo,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // VIEW表格数据
      vm_car_orderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        // 严格按照9个筛选条件
        // 1. 贷款人姓名
        customerName: null,
        // 2. 贷款人身份证号
        certId: null,
        // 3. 出单渠道
        jgName: null,
        // 4. 放款银行
        lendingBank: null,
        // 5. 法诉状态(多级)
        litigationStatus: null,
        // 6. 申请人
        applicationBy: null,
        // 7. 费用类型
        costCategory: null,
        // 8. 审批状态
        approvalStatus: null,
        // 9. 申请时间区间
        startTime: null,
        endTime: null,
        // 10. 审批时间区间
        approvalStartTime: null,
        approvalEndTime: null,
      },
      // 日期范围
      dateRange: [],
      // 审批日期范围
      approvalDateRange: [],
      // 表单参数
      form: {
        id:'',
        status: 0,
        rejectReason:null,
      },
      // 当前审批记录
      currentRecord: {},
      // 费用提交记录列表
      submissionRecords: [],
      // 记录加载状态
      recordsLoading: false,
      // 选中的记录
      selectedRecords: [],
      // 单个审批对话框
      singleApprovalOpen: false,
      singleApprovalForm: {
        id: '',
        status: '0',
        rejectReason: ''
      },
      // 批量审批对话框
      batchApprovalOpen: false,
      batchApprovalForm: {
        status: '0',
        rejectReason: ''
      },
      // 贷款人信息相关
      userInfoVisible: false,
      customerInfo: {},
      // 法诉状态多级选项
      litigationStatusOptions: [
        {
          value: '起诉',
          label: '起诉',
          children: [
            { value: '起诉-准备材料', label: '准备材料' },
            { value: '起诉-已提交', label: '已提交' },
            { value: '起诉-法院受理', label: '法院受理' }
          ]
        },
        {
          value: '审理',
          label: '审理',
          children: [
            { value: '审理-开庭审理', label: '开庭审理' },
            { value: '审理-等待判决', label: '等待判决' },
            { value: '审理-一审判决', label: '一审判决' }
          ]
        },
        {
          value: '执行',
          label: '执行',
          children: [
            { value: '执行-申请执行', label: '申请执行' },
            { value: '执行-执行中', label: '执行中' },
            { value: '执行-执行完毕', label: '执行完毕' }
          ]
        },
        {
          value: '结案',
          label: '结案',
          children: [
            { value: '结案-胜诉结案', label: '胜诉结案' },
            { value: '结案-败诉结案', label: '败诉结案' },
            { value: '结案-和解结案', label: '和解结案' }
          ]
        }
      ],
      // 表单校验
      rules: {
        keyProvince:'',
        keyCity:'',
        keyBorough:'',
        keyDetailAddress:'',
      },
      provinceList:areaList,
      cityList:[],
      districtList:[]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询法诉费用审批列表 */
    getList() {
      this.loading = true
      listLitigationCostApproval(this.queryParams).then(response => {
        this.vm_car_orderList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id:'',
        status: 0,
        rejectReason:null,
      }
      this.currentRecord = {}
      this.submissionRecords = []
      this.selectedRecords = []
      this.singleApprovalOpen = false
      this.batchApprovalOpen = false
      this.singleApprovalForm = {
        id: '',
        status: '0',
        rejectReason: ''
      }
      this.batchApprovalForm = {
        status: '0',
        rejectReason: ''
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.approvalDateRange = []
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加VIEW"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.currentRecord = row
      this.loadSubmissionRecords(row.litigationCaseId)
      this.open = true
    },

    /** 批量修改按钮操作 */
    handleBatchEdit() {
      this.$modal.msgError('请选择单条记录进行审批操作')
    },

    /** 加载费用提交记录 */
    loadSubmissionRecords(litigationCaseId) {
      this.recordsLoading = true
      getLitigationCostSubmissionRecords(litigationCaseId).then(response => {
        this.submissionRecords = response.data || []
        this.recordsLoading = false
      }).catch(() => {
        this.recordsLoading = false
      })
    },

    /** 记录选择变化 */
    handleRecordSelectionChange(selection) {
      this.selectedRecords = selection
    },

    /** 单个审批 */
    handleSingleApprove(record, status) {
      this.singleApprovalForm.id = record.id
      this.singleApprovalForm.status = status
      this.singleApprovalForm.rejectReason = ''
      this.singleApprovalOpen = true
    },

    /** 确认单个审批 */
    confirmSingleApproval() {
      if (this.singleApprovalForm.status == '1' && !this.singleApprovalForm.rejectReason) {
        this.$modal.msgError('请输入拒绝原因')
        return
      }

      approveLitigationCostRecord(this.singleApprovalForm).then(() => {
        this.$modal.msgSuccess('审批成功')
        this.singleApprovalOpen = false
        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)
        this.getList() // 刷新主列表
      })
    },

    /** 批量审批 */
    handleBatchApprove(status) {
      if (this.selectedRecords.length === 0) {
        this.$modal.msgError('请选择要审批的记录')
        return
      }

      this.batchApprovalForm.status = status
      this.batchApprovalForm.rejectReason = ''
      this.batchApprovalOpen = true
    },

    /** 确认批量审批 */
    confirmBatchApproval() {
      if (this.batchApprovalForm.status == '1' && !this.batchApprovalForm.rejectReason) {
        this.$modal.msgError('请输入拒绝原因')
        return
      }

      const data = {
        ids: this.selectedRecords.map(record => record.id),
        status: this.batchApprovalForm.status,
        rejectReason: this.batchApprovalForm.rejectReason
      }

      batchApproveLitigationCostRecords(data).then(() => {
        this.$modal.msgSuccess('批量审批成功')
        this.batchApprovalOpen = false
        this.selectedRecords = []
        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)
        this.getList() // 刷新主列表
      })
    },

    /** 主列表批量审批 */
    handleBatchApproveMain(status) {
      if (this.ids.length === 0) {
        this.$modal.msgError('请选择要审批的记录')
        return
      }

      const statusText = status === '0' ? '通过' : '拒绝'
      this.$modal.confirm(`确认要批量${statusText}选中的 ${this.ids.length} 条记录吗？`).then(() => {
        const data = {
          ids: this.ids,
          status: status,
          rejectReason: status === '1' ? '批量拒绝' : ''
        }

        return batchApproveLitigationCostRecords(data)
      }).then(() => {
        this.$modal.msgSuccess(`批量${statusText}成功`)
        this.getList()
      }).catch(() => {})
    },

    /** 删除按钮操作 */
    handleDelete() {
      this.$modal.confirm('是否确认删除选中的数据项？').then(() => {
        // 这里可以调用删除API，暂时只是提示
        this.$modal.msgSuccess("删除功能暂未实现")
      }).catch(() => {})
    },


    /** 导出按钮操作 */
    handleExport() {
      this.download('litigation_cost_approval/litigation_cost_approval/export', {
        ...this.queryParams
      }, `litigation_cost_approval_${new Date().getTime()}.xlsx`)
    },

    /** 打开贷款人信息 */
    openUserInfo(row) {
      if (!row.customerId && !row.applyId) {
        this.$modal.msgError('无法获取贷款人信息')
        return
      }

      this.customerInfo = {
        customerId: row.customerId,
        applyId: row.applyId,
        customerName: row.customerName
      }
      this.userInfoVisible = true
    },

    /** 处理日期范围变化 */
    handleDateRangeChange(dates) {
      if (dates && dates.length === 2) {
        this.queryParams.startTime = dates[0]
        this.queryParams.endTime = dates[1]
      } else {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      }
      this.handleQuery()
    },

    /** 处理审批时间范围变化 */
    handleApprovalDateRangeChange(dates) {
      if (dates && dates.length === 2) {
        this.queryParams.approvalStartTime = dates[0]
        this.queryParams.approvalEndTime = dates[1]
      } else {
        this.queryParams.approvalStartTime = null
        this.queryParams.approvalEndTime = null
      }
      this.handleQuery()
    }
  }
}
</script>

<style scoped>
.approval-header {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.batch-approval-section {
  border: 1px solid #e4e7ed;
  padding: 10px;
  border-radius: 4px;
  background-color: #fafafa;
}

.el-table {
  margin-top: 10px;
}

.el-tag {
  margin: 2px;
}
</style>
