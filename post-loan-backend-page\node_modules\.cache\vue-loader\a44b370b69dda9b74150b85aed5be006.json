{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationFeeForm.vue", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationFeeForm.vue", "mtime": 1754111180755}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfSBmcm9tICIuL2xpdGlnYXRpb25GZWVGb3JtLnZ1ZT92dWUmdHlwZT10ZW1wbGF0ZSZpZD05MTAwNmM1MCZzY29wZWQ9dHJ1ZSIKaW1wb3J0IHNjcmlwdCBmcm9tICIuL2xpdGlnYXRpb25GZWVGb3JtLnZ1ZT92dWUmdHlwZT1zY3JpcHQmbGFuZz1qcyIKZXhwb3J0ICogZnJvbSAiLi9saXRpZ2F0aW9uRmVlRm9ybS52dWU/dnVlJnR5cGU9c2NyaXB0Jmxhbmc9anMiCmltcG9ydCBzdHlsZTAgZnJvbSAiLi9saXRpZ2F0aW9uRmVlRm9ybS52dWU/dnVlJnR5cGU9c3R5bGUmaW5kZXg9MCZpZD05MTAwNmM1MCZzY29wZWQ9dHJ1ZSZsYW5nPWNzcyIKCgovKiBub3JtYWxpemUgY29tcG9uZW50ICovCmltcG9ydCBub3JtYWxpemVyIGZyb20gIiEuLi8uLi8uLi8uLi8uLi9ub2RlX21vZHVsZXMvdnVlLWxvYWRlci9saWIvcnVudGltZS9jb21wb25lbnROb3JtYWxpemVyLmpzIgp2YXIgY29tcG9uZW50ID0gbm9ybWFsaXplcigKICBzY3JpcHQsCiAgcmVuZGVyLAogIHN0YXRpY1JlbmRlckZucywKICBmYWxzZSwKICBudWxsLAogICI5MTAwNmM1MCIsCiAgbnVsbAogIAopCgovKiBob3QgcmVsb2FkICovCmlmIChtb2R1bGUuaG90KSB7CiAgdmFyIGFwaSA9IHJlcXVpcmUoIkQ6XFxjb2RlX3Byb2plY3RcXGphdmFfcHJvamVjdFxcbG9hblxccG9zdC1sb2FuLWJhY2tlbmQtcGFnZVxcbm9kZV9tb2R1bGVzXFx2dWUtaG90LXJlbG9hZC1hcGlcXGRpc3RcXGluZGV4LmpzIikKICBhcGkuaW5zdGFsbChyZXF1aXJlKCd2dWUnKSkKICBpZiAoYXBpLmNvbXBhdGlibGUpIHsKICAgIG1vZHVsZS5ob3QuYWNjZXB0KCkKICAgIGlmICghYXBpLmlzUmVjb3JkZWQoJzkxMDA2YzUwJykpIHsKICAgICAgYXBpLmNyZWF0ZVJlY29yZCgnOTEwMDZjNTAnLCBjb21wb25lbnQub3B0aW9ucykKICAgIH0gZWxzZSB7CiAgICAgIGFwaS5yZWxvYWQoJzkxMDA2YzUwJywgY29tcG9uZW50Lm9wdGlvbnMpCiAgICB9CiAgICBtb2R1bGUuaG90LmFjY2VwdCgiLi9saXRpZ2F0aW9uRmVlRm9ybS52dWU/dnVlJnR5cGU9dGVtcGxhdGUmaWQ9OTEwMDZjNTAmc2NvcGVkPXRydWUiLCBmdW5jdGlvbiAoKSB7CiAgICAgIGFwaS5yZXJlbmRlcignOTEwMDZjNTAnLCB7CiAgICAgICAgcmVuZGVyOiByZW5kZXIsCiAgICAgICAgc3RhdGljUmVuZGVyRm5zOiBzdGF0aWNSZW5kZXJGbnMKICAgICAgfSkKICAgIH0pCiAgfQp9CmNvbXBvbmVudC5vcHRpb25zLl9fZmlsZSA9ICJzcmMvdmlld3MvbGl0aWdhdGlvbi9saXRpZ2F0aW9uL21vZHVsZXMvbGl0aWdhdGlvbkZlZUZvcm0udnVlIgpleHBvcnQgZGVmYXVsdCBjb21wb25lbnQuZXhwb3J0cw=="}]}