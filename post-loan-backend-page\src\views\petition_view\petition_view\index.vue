<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="" prop="customerName">
        <el-input v-model="queryParams.customerName" placeholder="贷款人账户、姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="certId">
        <el-input v-model="queryParams.certId" placeholder="贷款人身份证号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="plateNo">
        <el-input v-model="queryParams.plateNo" placeholder="车牌号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="salesman">
        <el-input v-model="queryParams.salesman" placeholder="业务员姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="jgName">
        <el-input v-model="queryParams.jgName" placeholder="录单渠道名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="partnerId">
        <el-select v-model="queryParams.partnerId" placeholder="放款银行" clearable>
          <el-option v-for="dict in bankList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="petitionName">
        <el-input v-model="queryParams.petitionName" placeholder="上访员姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="followStatus">
        <el-select v-model="queryParams.followStatus" placeholder="跟催类型" clearable>
          <el-option v-for="dict in followUpList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="dispatchStatus">
        <el-select v-model="queryParams.dispatchStatus" placeholder="派单状态" clearable>
          <el-option v-for="dict in dispatchList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="指派时间">
        <el-date-picker
          v-model="queryParams.allocationTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="车辆状态">
        <el-select v-model="queryParams.carStatus" placeholder="车辆状态" clearable>
          <el-option v-for="dict in carStatusList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否派单找车">
        <el-select v-model="queryParams.isFindCar" placeholder="是否派单找车" clearable>
          <el-option label="未派单" :value="0" />
          <el-option label="已派单" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="vw_account_loanList">
      <el-table-column label="序号" align="center" type="index" width="55" fixed="left" />
      <el-table-column label="逾期状态" align="center" prop="slippageStatus">
        <template slot-scope="scope">
          <span>
            {{
              scope.row.slippageStatus == 1
                ? '提醒'
                : scope.row.slippageStatus == 2
                  ? '电催'
                  : scope.row.slippageStatus == 3
                    ? '上访'
                    : scope.row.slippageStatus == 4
                      ? '逾期30-60'
                      : '逾期60+'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="还款状态" align="center" prop="repaymentStatus" width="130">
        <template slot-scope="scope">
          <span>
            {{
              scope.row.repaymentStatus == 1
                ? '还款中'
                : scope.row.repaymentStatus == 2
                  ? '已完结'
                  : scope.row.repaymentStatus == 3
                    ? '提前结清'
                    : scope.row.repaymentStatus == 4
                      ? '逾期催回结清'
                      : scope.row.repaymentStatus == 5
                        ? '逾期减免结清'
                        : scope.row.repaymentStatus == 6
                          ? '逾期未还款'
                          : scope.row.repaymentStatus == 7
                            ? '逾期还款中'
                            : scope.row.repaymentStatus == 8
                              ? '代偿未还款'
                              : scope.row.repaymentStatus == 9
                                ? '代偿还款中'
                                : scope.row.repaymentStatus == 10
                                  ? '代偿减免结清'
                                  : scope.row.repaymentStatus == 11
                                    ? '代偿全额结清'
                                    : '未知状态'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="贷款人" align="center" prop="customerName">
        <template slot-scope="scope">
          <el-button type="text" @click="openUserInfo({ customerId: scope.row.customerId, applyId: scope.row.applyId })">
            {{ scope.row.customerName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="出单渠道" align="center" prop="jgName" />
      <el-table-column label="业务员" align="center" prop="nickName" />
      <el-table-column label="车牌号码" align="center" prop="plateNo">
        <template slot-scope="scope">
          <el-button type="text" @click="openCarInfo(scope.row.plateNo)">{{ scope.row.plateNo }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="车辆状态" align="center" prop="carStatus">
        <template slot-scope="scope">
          <span v-if="scope.row.carStatus != null">
            {{
              (carStatusList.find(item => item.value === String(scope.row.carStatus)) || {}).label || ''
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="派车团队" align="center" prop="carTeamName">
        <template slot-scope="scope">
          <span v-if="scope.row.carTeamName">{{ scope.row.carTeamName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="位置" align="center" prop="carDetailAddress" />
      <el-table-column label="GPS状态" align="center" prop="gpsStatus" />
      <el-table-column label="派车单状态" align="center" prop="loanStatus" width="130" />
      <el-table-column label="放款银行" align="center" prop="orgName" />
      <el-table-column label="逾期天数" align="center" prop="boverdueDays" width="130" />
      <el-table-column label="首期逾期金额" align="center" prop="foverdueAmount" width="130" />
      <el-table-column label="银行逾期金额" align="center" prop="boverdueAmount" width="130" />
      <el-table-column label="代扣逾期金额" align="center" prop="doverdueAmount" width="130" />
      <el-table-column label="银行结清金额" align="center" prop="bSettleAmount" width="130" />
      <el-table-column label="代扣结清金额" align="center" prop="dSettleAmount" width="130" />
      <el-table-column label="上访员" align="center" prop="petitionUser" width="130" />
      <el-table-column label="跟催类型" align="center" prop="urgeType">
        <template slot-scope="scope">
          <span v-if="scope.row.urgeType != null">
            {{ scope.row.urgeType == 1 ? '继续联系' : scope.row.urgeType == 2 ? '约定还款' : '无法跟进' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="催记日期" align="center" prop="assignTime" width="130">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.assignTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分配时间" align="center" prop="petitionTime" width="130" />
      <el-table-column label="下次跟进时间" align="center" prop="nextFollowTime" width="130" />
      <el-table-column label="预扣款时间" align="center" prop="preDeductionTime" width="130" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="80">
        <template slot-scope="scope">
          <el-popover placement="left" trigger="click" popper-class="custom-popover">
            <div class="operation-buttons">
              <el-button
                class="operation-btn"
                size="mini"
                type="text"
                @click="initiate(scope.row)"
                v-hasPermi="['vw_account_loan:vw_account_loan:edit']">
                发起代偿
              </el-button>
              <el-button
                class="operation-btn"
                size="mini"
                type="text"
                @click="openDispatchVehicleForm(scope.row)"
                v-hasPermi="['vw_account_loan:vw_account_loan:edit']">
                发起找车
              </el-button>
              <el-button
                class="operation-btn"
                size="mini"
                type="text"
                @click="logView(scope.row)"
                v-hasPermi="['vw_account_loan:vw_account_loan:edit']">
                查看催记
              </el-button>
              <el-button
                class="operation-btn"
                size="mini"
                type="text"
                @click="openDailyExpenseDialog(scope.row)"
                v-hasPermi="['daily_expense_approval:daily_expense_approval:add']">
                提交日常费用
              </el-button>
            </div>
            <el-button slot="reference" size="mini" type="text">更多</el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 发起代偿对话框 -->
    <el-dialog :close-on-click-modal="false" class="dialogBox" title="发起代偿" :visible.sync="commuteopen" width="900px" append-to-body>
      <div class="settle_money" @click="trialSub">发起试算</div>
      <el-form ref="trialForm" :model="trialForm" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="贷款人" prop="customerName">
              <el-input v-model="trialForm.customerName" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出单渠道" prop="orgName">
              <el-input v-model="trialForm.orgName" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="放款银行" prop="bank">
              <el-input v-model="trialForm.bank" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="放款金额" prop="loanAmount">
              <el-input v-model="trialForm.loanAmount" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="剩余本金">
              <el-input v-model="trialFormprincipal" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="银行逾期金额">
              <el-input v-model="trialFormboverdueAmount" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="银行利息">
              <el-input v-model="trialForminterest" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="代偿总金额">
              <el-input v-model="trialFormall" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="风险金划比例" prop="fxjProportion">
              <el-input v-model="trialForm.fxjProportion" @input="fxjCount">
                <template #append>%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="风险金划金额" prop="fxjMoney">
              <el-input v-model="trialForm.fxjMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账号类型" prop="fxjAccount">
              <el-select v-model="trialForm.fxjAccount" placeholder="账号类型" clearable>
                <el-option v-for="dict in accountList" :key="dict.card" :label="dict.name" :value="dict.card" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="渠道转入比例" prop="qdProportion">
              <el-input v-model="trialForm.qdProportion" @input="qdCount">
                <template #append>%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="渠道转入金额" prop="qdMoney">
              <el-input v-model="trialForm.qdMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账号类型" prop="qdAccount">
              <el-select v-model="trialForm.qdAccount" placeholder="账号类型" clearable>
                <el-option v-for="dict in accountList" :key="dict.card" :label="dict.name" :value="dict.card" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="广明借比例" prop="gmjProportion">
              <el-input v-model="trialForm.gmjProportion" @input="gmjCount">
                <template #append>%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="广明借额" prop="gmjMoney">
              <el-input v-model="trialForm.gmjMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账号类型" prop="gmjAccount">
              <el-select v-model="trialForm.gmjAccount" placeholder="账号类型" clearable>
                <el-option v-for="dict in accountList" :key="dict.card" :label="dict.name" :value="dict.card" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="科技借比例" prop="kjczProportion">
              <el-input v-model="trialForm.kjczProportion" @input="kjjCount">
                <template #append>%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="科技借额" prop="kjjMoney">
              <el-input v-model="trialForm.kjjMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账号类型" prop="kjjAccount">
              <el-select v-model="trialForm.kjjAccount" placeholder="账号类型" clearable>
                <el-option v-for="dict in accountList" :key="dict.card" :label="dict.name" :value="dict.card" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="科技出资比例" prop="kjczProportion">
              <el-input v-model="trialForm.kjczProportion" @input="kjczCount">
                <template #append>%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="科技出资金额" prop="kjczMoney">
              <el-input v-model="trialForm.kjczMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账号类型" prop="kjczAccount">
              <el-select v-model="trialForm.kjczAccount" placeholder="账号类型" clearable>
                <el-option v-for="dict in accountList" :key="dict.card" :label="dict.name" :value="dict.card" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="守邦出资比例" prop="sbczProportion">
              <el-input v-model="trialForm.sbczProportion" @input="sbczCount">
                <template #append>%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="守邦出资金额" prop="sbczMoney">
              <el-input v-model="trialForm.sbczMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账号类型" prop="sbczAccount">
              <el-select v-model="trialForm.sbczAccount" placeholder="账号类型" clearable>
                <el-option v-for="dict in accountList" :key="dict.card" :label="dict.name" :value="dict.card" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="代扣剩余未还金额">
              <el-input v-model="trialFormdoverdueAmount" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="违约金">
              <el-input v-model="trialFormliquidatedDamages" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="登记其他欠款金额" prop="otherDebt">
              <el-input type="number" @input="handleInput3" v-model="trialFormotherDebt" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总账款">
              <el-input v-model="trialFormtotal" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="trialForm.examineStatus != 1" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel('commuteopen')">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 贷款人信息对话框 -->
    <userInfo ref="userInfo" :visible.sync="userInfoVisible" title="贷款人信息" :customerInfo="customerInfo" />
    <!-- 车辆信息 -->
    <carInfo ref="carInfo" :visible.sync="carInfoVisible" title="车辆信息" :plateNo="plateNo" permission="2" />

    <!-- 催记日志组件 -->
    <loan-reminder-log ref="loanReminderLog" :loan-id="currentRow.loanId" />
    <DispatchVehicleForm ref="dispatchVehicleForm" :loanId="dispatchLoanId" />

    <!-- 日常费用申请弹窗 -->
    <el-dialog :title="getDailyExpenseDialogTitle()" :visible.sync="dailyExpenseDialogVisible" width="600px" append-to-body>
      <el-form ref="dailyExpenseForm" :model="dailyExpenseForm" :rules="dailyExpenseRules" label-width="120px">
        <el-form-item label="费用类型" prop="expenseType">
          <el-select v-model="dailyExpenseForm.expenseType" placeholder="请选择费用类型" style="width: 100%">
            <el-option label="油费" value="oil_fee" />
            <el-option label="路费" value="road_fee" />
            <el-option label="餐费" value="meal_fee" />
            <el-option label="住宿费" value="accommodation_fee" />
            <el-option label="交通费" value="transport_fee" />
            <el-option label="停车费" value="parking_fee" />
            <el-option label="通讯费" value="communication_fee" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="费用金额" prop="expenseAmount">
          <el-input v-model="dailyExpenseForm.expenseAmount" placeholder="请输入费用金额">
            <template slot="prepend">￥</template>
          </el-input>
        </el-form-item>
        <el-form-item label="费用发生日期" prop="expenseDate">
          <el-date-picker
            v-model="dailyExpenseForm.expenseDate"
            type="date"
            placeholder="请选择费用发生日期"
            value-format="yyyy-MM-dd"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="费用说明" prop="expenseDescription">
          <el-input
            v-model="dailyExpenseForm.expenseDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入费用说明">
          </el-input>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDailyExpense">确 定</el-button>
        <el-button @click="cancelDailyExpense">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listVw_account_loan } from '@/api/petition_view/petition_view'
import { listCar_team } from '@/api/car_team/car_team'
import { addCar_order } from '@/api/car_order/car_order'
import { Message } from 'element-ui'
import { get_bank_account, dc_submit_order, loan_compensation_order } from '@/api/vw_account_loan/vw_account_loan'
import { addDaily_expense_approval } from '@/api/daily_expense_approval/daily_expense_approval'
import userInfo from '@/layout/components/Dialog/userInfo.vue'
import carInfo from '@/layout/components/Dialog/carInfo.vue'
import LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'
import DispatchVehicleForm from '@/layout/components/Dialog/dispatchVehicleForm.vue'

export default {
  name: 'Vw_account_loan',
  components: {
    userInfo,
    carInfo,
    LoanReminderLog,
    DispatchVehicleForm,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // VIEW表格数据
      vw_account_loanList: [],
      // 弹出层标题
      title: '',
      // 是否弹出发起代偿对话框
      commuteopen: false,
      trialForm: {},
      trialFormprincipal: 0,
      trialFormboverdueAmount: 0,
      trialForminterest: 0,
      trialFormall: 0,
      trialFormdoverdueAmount: 0,
      trialFormliquidatedDamages: 0,
      trialFormtotal: 0,
      trialFormotherDebt: 0,
      accountList: [], //银行账户列表
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 15,
        customerName: null,
        applyNo: null, // 更新为后端字段
        plateNo: null,
        salesman: null,
        jgName: null,
        partnerId: null,
        petitionName: null,
        followStatus: null,
        dispatchStatus: null,
        allocationTime: null,
        startTime: null,
        endTime: null,
        slippageStatus: 3,
        teamId: null,
        garageId: null,
        libraryStatus: null,
        inboundTime: null,
        outboundTime: null,
        locatingCommission: null,
        GPS: null,
        keyStatus: null,
        keyTime: null,
        collectionMethod: null,
        status: null,
        keyProvince: null,
        keyCity: null,
        keyBorough: null,
        keyAddress: null,
        keyDetailAddress: null,
        carStatus: null,
        isFindCar: null,
      },
      bankList: [
        { value: 'EO00000010', label: '苏银金租' },
        { value: 'IO00000006', label: '浙商银行' },
        { value: 'IO00000007', label: '中关村银行' },
        { value: 'IO00000008', label: '蓝海银行' },
        { value: 'IO00000009', label: '华瑞银行' },
        { value: 'IO00000010', label: '皖新租赁' },
      ],
      followUpList: [
        { label: '无法跟进', value: 1 },
        { label: '约定还款', value: 2 },
        { label: '继续联系', value: 3 },
      ],
      dispatchList: [
        { label: '待派单', value: 1 },
        { label: '找车中', value: 2 },
        { label: '已入库', value: 3 },
        { label: '未派单', value: 4 },
        { label: '已撤销', value: 5 },
      ],
      carStatusList: [
        { label: '省内正常行驶', value: '1' },
        { label: '省外正常行驶', value: '2' },
        { label: '抵押', value: '3' },
        { label: '疑似抵押', value: '4' },
        { label: '疑似黑车', value: '5' },
        { label: '已入库', value: '6' },
        { label: '车在法院', value: '7' },
        { label: '已法拍', value: '8' },
        { label: '协商卖车', value: '9' },
      ],
      // 表单参数
      form: {
        customerName: '',
        mobilePhone: '',
        contractId: '',
        customerId: '',
        plateNo: '',
        carStatus: '',
        carDetailAddress: '',
        gpsStatus: '',
        followUpType: '',
        applyNo: '',
        teamId: null,
        garageId: null,
        libraryStatus: null,
        inboundTime: '',
        outboundTime: '',
        locatingCommission: null,
        keyStatus: null,
        keyTime: '',
        collectionMethod: null,
        status: null,
        keyProvince: '',
        keyCity: '',
        keyBorough: '',
        keyAddress: '',
        keyDetailAddress: '',
      },
      // 表单校验规则
      rules: {},
      radio: 0,
      // 添加新的数据属性
      customerInfo: { customerId: '', applyId: '' },
      userInfoVisible: false,
      plateNo: '',
      carInfoVisible: false,
      currentRow: {},
      dispatchLoanId: null,
      // 日常费用申请相关
      dailyExpenseDialogVisible: false,
      currentLoanRow: {},
      dailyExpenseForm: {
        litigationCaseId: null,
        loanId: null,
        status: 2, // 上访提交
        expenseType: '',
        expenseAmount: '',
        expenseDate: '',
        expenseDescription: '',
        receiptUrl: '',
        applicantId: '',
        applicantName: '',
        applicationTime: '',
        approvalStatus: '0'
      },
      dailyExpenseRules: {
        expenseType: [{ required: true, message: '请选择费用类型', trigger: 'change' }],
        expenseAmount: [
          { required: true, message: '请输入费用金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        expenseDate: [{ required: true, message: '请选择费用发生日期', trigger: 'change' }],
        expenseDescription: [{ required: true, message: '请输入费用说明', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
    this.getCarTeam()
    this.getBankList()
  },
  methods: {
    /** 查询VIEW列表 */
    getList() {
      this.loading = true
      listVw_account_loan(this.queryParams)
        .then(response => {
          this.vw_account_loanList = response.rows
          this.total = response.total
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    fxjCount(val) {
      this.trialForm.fxjMoney = Number(Number(val) * this.trialFormall) / 100
    },
    qdCount(val) {
      this.trialForm.qdMoney = Number(Number(val) * this.trialFormall) / 100
    },
    gmjCount(val) {
      this.trialForm.gmjMoney = Number(Number(val) * this.trialFormall) / 100
    },
    kjjCount(val) {
      this.trialForm.kjjMoney = Number(Number(val) * this.trialFormall) / 100
    },
    kjczCount(val) {
      this.trialForm.kjczMoney = Number(Number(val) * this.trialFormall) / 100
    },
    sbczCount(val) {
      this.trialForm.sbczMoney = Number(Number(val) * this.trialFormall) / 100
    },
    handleInput3(value) {
      this.trialFormotherDebt = Number(value)
      this.trialFormtotal = Number(
        this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt
      ).toFixed(2)
    },
    trialSub() {
      var data = {
        applyId: this.trialForm.applyId,
        id: this.trialForm.id,
        loanId: this.trialForm.loanId,
        loanAmount: this.trialForm.loanAmount,
        partnerId: this.trialForm.partnerId,
      }
      dc_submit_order(data).then(response => {
        this.trialFormprincipal = response.data.principal || 0
        this.trialForm.defaultInterest = response.data.defaultInterest || 0
        this.trialForminterest = response.data.interest || 0
        this.trialFormall = response.data.btotalMoney || 0
        this.trialFormdoverdueAmount = response.data.dtotalMoney || 0
        this.trialFormliquidatedDamages = response.data.liquidatedDamages || 0

        // this.trialFormboverdueAmount = Number(
        //   this.trialFormprincipal +
        //     this.trialForminterest +
        //     this.trialForm.defaultInterest
        // ).toFixed(2);
        // this.trialFormboverdueAmount = this.trialFormboverdueAmount
        this.trialFormtotal = Number(
          this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt
        ).toFixed(2)
      })
    },
    getBankList() {
      get_bank_account().then(response => {
        this.accountList = response.rows
      })
    },
    getCarTeam() {
      this.loading = true
      listCar_team({ status: 1 })
        .then(response => {
          this.car_team = response.rows
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 取消按钮
    cancel(dialog) {
      if (dialog === 'commuteopen') {
        this.commuteopen = false
      }
      this.reset()
    },
    // 表单重置
    reset() {
      this.$refs.form?.resetFields() // 重置表单验证
      this.form = {}
      this.queryParams.carStatus = null
      this.queryParams.isFindCar = null
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.queryParams.allocationTime) {
        this.queryParams.startTime = this.queryParams.allocationTime[0]
        this.queryParams.endTime = this.queryParams.allocationTime[1]
      }
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.customerName = null
      this.queryParams.certId = null
      this.queryParams.plateNo = null
      this.queryParams.salesman = null
      this.queryParams.jgName = null
      this.queryParams.partnerId = null
      this.queryParams.petitionName = null
      this.queryParams.followStatus = null
      this.queryParams.dispatchStatus = null
      this.queryParams.allocationTime = null
      this.queryParams.startTime = null
      this.queryParams.endTime = null
      this.queryParams.carStatus = null
      this.queryParams.isFindCar = null
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      // This method is no longer needed as selection is removed.
      // Keeping it for now as it might be used elsewhere or for future changes.
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.open = true
      this.title = '修改VIEW'
    },
    /** 发起代偿操作 */
    initiate(row) {
      //发起代偿
      var data = {
        loanId: row.loanId,
      }
      loan_compensation_order(data).then(response => {
        if (response.data) {
          this.trialForm = response.data
          this.trialForm.loanAmount = row.contractAmt || 0
          this.trialForm.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0
          this.trialFormprincipal = response.data.trialBalance ? response.data.trialBalance.principal : 0
          this.trialFormboverdueAmount = row.boverdueAmount || 0
          this.trialForminterest = response.data.trialBalance ? response.data.trialBalance.interest : 0
          this.trialFormall = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0
          this.trialFormdoverdueAmount = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0
          this.trialFormliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0
          this.trialFormotherDebt = response.data.otherDebt ? response.data.otherDebt : 0
          console.log(this.trialFormall, this.trialFormdoverdueAmount, this.trialFormliquidatedDamages, this.trialFormotherDebt)
          this.trialFormtotal = Number(
            this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt
          ).toFixed(2)
        } else {
          this.addTrial(row)
        }
        this.commuteopen = true
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 调用后端接口添加找车订单
          addCar_order(this.form)
            .then(response => {
              if (response.code === 200) {
                Message({
                  message: '找车订单添加成功',
                  type: 'success',
                  duration: 3 * 1000,
                })
                this.getList() // 刷新列表
                this.reset() // 重置表单
              } else {
                Message({
                  message: '添加失败: ' + response.message,
                  type: 'error',
                  duration: 3 * 1000,
                })
              }
            })
            .catch(error => {
              Message({
                message: '服务器错误: ' + error.message,
                type: 'error',
                duration: 3 * 1000,
              })
            })
        } else {
          console.log('表单验证失败')
          return false
        }
      })
    },
    // 添加新的方法
    openUserInfo(customerInfo) {
      this.customerInfo = customerInfo
      this.userInfoVisible = true
    },
    openCarInfo(plateNo) {
      this.plateNo = plateNo
      this.carInfoVisible = true
    },
    // 查看催记日志
    logView(row) {
      this.currentRow = row
      this.$refs.loanReminderLog.openLogDialog()
    },
    openDispatchVehicleForm(row) {
      this.dispatchLoanId = row.loanId
      this.$refs.dispatchVehicleForm.openDialog()
    },
    // 打开日常费用申请弹窗
    openDailyExpenseDialog(row) {
      this.resetDailyExpenseForm()
      this.dailyExpenseForm.applicantId = String(this.$store.state.user?.id || '')
      this.dailyExpenseForm.applicantName = String(this.$store.state.user?.name || '')
      // 设置当前日期作为申请时间
      const today = new Date()
      this.dailyExpenseForm.applicationTime = today.toISOString().split('T')[0]
      // 设置贷款ID和状态（上访提交）
      this.dailyExpenseForm.loanId = row.loanId
      this.dailyExpenseForm.status = 2 // 上访提交
      this.currentLoanRow = row
      this.dailyExpenseDialogVisible = true
    },
    // 重置日常费用表单
    resetDailyExpenseForm() {
      this.dailyExpenseForm = {
        litigationCaseId: null,
        loanId: null,
        status: 2, // 上访提交
        expenseType: '',
        expenseAmount: '',
        expenseDate: '',
        expenseDescription: '',
        receiptUrl: '',
        applicantId: '',
        applicantName: '',
        applicationTime: '',
        approvalStatus: '0'
      }
      this.currentLoanRow = {}
      this.$refs.dailyExpenseForm?.resetFields()
    },
    // 取消日常费用申请
    cancelDailyExpense() {
      this.dailyExpenseDialogVisible = false
      this.resetDailyExpenseForm()
    },
    // 提交日常费用申请
    submitDailyExpense() {
      this.$refs.dailyExpenseForm.validate((valid) => {
        if (valid) {
          // 上访提交：确保有 loanId 和 status = 2
          const submitData = { ...this.dailyExpenseForm }
          if (!submitData.loanId) {
            this.$message.error('缺少贷款ID，请重新选择记录')
            return
          }
          submitData.status = 2 // 确保是上访提交
          addDaily_expense_approval(submitData).then(res => {
            if (res.code === 200) {
              this.$message.success('日常费用申请提交成功')
              this.dailyExpenseDialogVisible = false
              this.resetDailyExpenseForm()
            } else {
              this.$message.error('提交失败：' + (res.msg || '未知错误'))
            }
          }).catch(error => {
            console.error('提交失败:', error)
            this.$message.error('提交失败，请稍后重试')
          })
        }
      })
    },
    // 获取日常费用弹窗标题
    getDailyExpenseDialogTitle() {
      if (this.currentLoanRow && this.currentLoanRow.customerName) {
        return `提交日常费用 - ${this.currentLoanRow.customerName}`
      }
      return '提交日常费用'
    }
  },
}
</script>
<style>
.dialogBox .el-form-item__label {
  width: 100px !important;
}

.dialogBox .el-form-item__content {
  margin-left: 100px !important;
}

.warnBox {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-bottom: 10px;
}

.warnImg {
  width: 20px;
  height: 20px;
  margin: 0 5px 0 10px;
}

.settle_money {
  background-color: #1890ff;
  color: #fff;
  border-radius: 5px;
  display: inline-block;
  padding: 5px 10px;
  box-sizing: border-box;
  margin-bottom: 10px;
}
/* 操作按钮容器 */
.operation-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 90px;
  padding: 4px 8px;
}

/* 操作按钮样式 */
.operation-btn {
  width: 74px !important;
  height: 26px !important;
  margin: 0 !important;
  padding: 0 2px !important;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-size: 12px;
  white-space: nowrap;
  text-align: center;
  line-height: 26px;
}

/* 按钮悬停效果 */
.operation-btn:hover {
  background-color: #f5f7fa;
  color: #409eff;
}
</style>
<style>
.custom-popover {
  width: 116px !important;
  min-width: 116px !important;
  max-width: 116px !important;
  box-sizing: border-box !important;
}
</style>
