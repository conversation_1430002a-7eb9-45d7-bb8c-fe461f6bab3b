<template>
  <div class="approval-progress">
    <el-steps :active="currentStep" :status="stepStatus" align-center>
      <el-step
        v-for="(step, index) in steps"
        :key="index"
        :title="step.title"
        :description="step.description"
        :status="step.status"
      ></el-step>
    </el-steps>
    
    <div v-if="rejectReason" class="reject-reason">
      <el-alert
        title="拒绝原因"
        type="error"
        :description="rejectReason"
        show-icon
        :closable="false"
      ></el-alert>
    </div>
  </div>
</template>

<script>
import ApprovalManager, { APPROVAL_STATUS, APPROVAL_FLOW } from "@/utils/approvalStatus"

export default {
  name: "ApprovalProgress",
  props: {
    status: {
      type: Number,
      required: true
    },
    rejectReason: {
      type: String,
      default: ''
    }
  },
  computed: {
    steps() {
      const steps = [
        {
          title: '法诉主管审批',
          description: '法务诉讼主管审批',
          status: this.getStepStatus(APPROVAL_STATUS.LEGAL_SUPERVISOR)
        },
        {
          title: '总监审批',
          description: '部门总监审批',
          status: this.getStepStatus(APPROVAL_STATUS.DIRECTOR)
        },
        {
          title: '财务主管/总监抄送',
          description: '财务部门审批',
          status: this.getStepStatus(APPROVAL_STATUS.FINANCE_SUPERVISOR)
        },
        {
          title: '总经理/董事长审批',
          description: '最高管理层审批',
          status: this.getStepStatus(APPROVAL_STATUS.GENERAL_MANAGER)
        }
      ]
      return steps
    },
    currentStep() {
      if (this.status === APPROVAL_STATUS.PENDING) {
        return 0
      }
      if (this.status === APPROVAL_STATUS.REJECTED) {
        // 找到当前被拒绝的步骤
        const currentIndex = APPROVAL_FLOW.indexOf(this.getCurrentApprovalNode())
        return currentIndex >= 0 ? currentIndex : 0
      }
      if (this.status === APPROVAL_STATUS.APPROVED) {
        return APPROVAL_FLOW.length
      }
      
      const currentIndex = APPROVAL_FLOW.indexOf(this.status)
      return currentIndex >= 0 ? currentIndex : 0
    },
    stepStatus() {
      if (this.status === APPROVAL_STATUS.REJECTED) {
        return 'error'
      }
      if (this.status === APPROVAL_STATUS.APPROVED) {
        return 'success'
      }
      return 'process'
    }
  },
  methods: {
    getStepStatus(stepStatus) {
      if (this.status === APPROVAL_STATUS.REJECTED) {
        const currentIndex = APPROVAL_FLOW.indexOf(this.getCurrentApprovalNode())
        const stepIndex = APPROVAL_FLOW.indexOf(stepStatus)
        if (stepIndex < currentIndex) {
          return 'success'
        } else if (stepIndex === currentIndex) {
          return 'error'
        } else {
          return 'wait'
        }
      }
      
      if (this.status === APPROVAL_STATUS.APPROVED) {
        return 'success'
      }
      
      const currentIndex = APPROVAL_FLOW.indexOf(this.status)
      const stepIndex = APPROVAL_FLOW.indexOf(stepStatus)
      
      if (stepIndex < currentIndex) {
        return 'success'
      } else if (stepIndex === currentIndex) {
        return 'process'
      } else {
        return 'wait'
      }
    },
    getCurrentApprovalNode() {
      // 这里需要根据实际情况确定当前被拒绝的节点
      // 可以通过额外的参数传入，或者从后端获取
      return APPROVAL_FLOW[0] // 默认返回第一个节点
    }
  }
}
</script>

<style scoped>
.approval-progress {
  padding: 20px;
}

.reject-reason {
  margin-top: 20px;
}

.el-steps {
  margin-bottom: 20px;
}
</style>
