{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es.function.name.js\");\nrequire(\"core-js/modules/es.number.constructor.js\");\nrequire(\"core-js/modules/es.number.to-fixed.js\");\nvar _petition_view = require(\"@/api/petition_view/petition_view\");\nvar _car_team = require(\"@/api/car_team/car_team\");\nvar _car_order = require(\"@/api/car_order/car_order\");\nvar _elementUi = require(\"element-ui\");\nvar _vw_account_loan = require(\"@/api/vw_account_loan/vw_account_loan\");\nvar _daily_expense_approval = require(\"@/api/daily_expense_approval/daily_expense_approval\");\nvar _userInfo = _interopRequireDefault(require(\"@/layout/components/Dialog/userInfo.vue\"));\nvar _carInfo = _interopRequireDefault(require(\"@/layout/components/Dialog/carInfo.vue\"));\nvar _loanReminderLog = _interopRequireDefault(require(\"@/layout/components/Dialog/loanReminderLog.vue\"));\nvar _dispatchVehicleForm = _interopRequireDefault(require(\"@/layout/components/Dialog/dispatchVehicleForm.vue\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: 'Vw_account_loan',\n  components: {\n    userInfo: _userInfo.default,\n    carInfo: _carInfo.default,\n    LoanReminderLog: _loanReminderLog.default,\n    DispatchVehicleForm: _dispatchVehicleForm.default\n  },\n  data: function data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // VIEW表格数据\n      vw_account_loanList: [],\n      // 弹出层标题\n      title: '',\n      // 是否弹出发起代偿对话框\n      commuteopen: false,\n      trialForm: {},\n      trialFormprincipal: 0,\n      trialFormboverdueAmount: 0,\n      trialForminterest: 0,\n      trialFormall: 0,\n      trialFormdoverdueAmount: 0,\n      trialFormliquidatedDamages: 0,\n      trialFormtotal: 0,\n      trialFormotherDebt: 0,\n      accountList: [],\n      //银行账户列表\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 15,\n        customerName: null,\n        applyNo: null,\n        // 更新为后端字段\n        plateNo: null,\n        salesman: null,\n        jgName: null,\n        partnerId: null,\n        petitionName: null,\n        followStatus: null,\n        dispatchStatus: null,\n        allocationTime: null,\n        startTime: null,\n        endTime: null,\n        slippageStatus: 3,\n        teamId: null,\n        garageId: null,\n        libraryStatus: null,\n        inboundTime: null,\n        outboundTime: null,\n        locatingCommission: null,\n        GPS: null,\n        keyStatus: null,\n        keyTime: null,\n        collectionMethod: null,\n        status: null,\n        keyProvince: null,\n        keyCity: null,\n        keyBorough: null,\n        keyAddress: null,\n        keyDetailAddress: null,\n        carStatus: null,\n        isFindCar: null\n      },\n      bankList: [{\n        value: 'EO00000010',\n        label: '苏银金租'\n      }, {\n        value: 'IO00000006',\n        label: '浙商银行'\n      }, {\n        value: 'IO00000007',\n        label: '中关村银行'\n      }, {\n        value: 'IO00000008',\n        label: '蓝海银行'\n      }, {\n        value: 'IO00000009',\n        label: '华瑞银行'\n      }, {\n        value: 'IO00000010',\n        label: '皖新租赁'\n      }],\n      followUpList: [{\n        label: '无法跟进',\n        value: 1\n      }, {\n        label: '约定还款',\n        value: 2\n      }, {\n        label: '继续联系',\n        value: 3\n      }],\n      dispatchList: [{\n        label: '待派单',\n        value: 1\n      }, {\n        label: '找车中',\n        value: 2\n      }, {\n        label: '已入库',\n        value: 3\n      }, {\n        label: '未派单',\n        value: 4\n      }, {\n        label: '已撤销',\n        value: 5\n      }],\n      carStatusList: [{\n        label: '省内正常行驶',\n        value: '1'\n      }, {\n        label: '省外正常行驶',\n        value: '2'\n      }, {\n        label: '抵押',\n        value: '3'\n      }, {\n        label: '疑似抵押',\n        value: '4'\n      }, {\n        label: '疑似黑车',\n        value: '5'\n      }, {\n        label: '已入库',\n        value: '6'\n      }, {\n        label: '车在法院',\n        value: '7'\n      }, {\n        label: '已法拍',\n        value: '8'\n      }, {\n        label: '协商卖车',\n        value: '9'\n      }],\n      // 表单参数\n      form: {\n        customerName: '',\n        mobilePhone: '',\n        contractId: '',\n        customerId: '',\n        plateNo: '',\n        carStatus: '',\n        carDetailAddress: '',\n        gpsStatus: '',\n        followUpType: '',\n        applyNo: '',\n        teamId: null,\n        garageId: null,\n        libraryStatus: null,\n        inboundTime: '',\n        outboundTime: '',\n        locatingCommission: null,\n        keyStatus: null,\n        keyTime: '',\n        collectionMethod: null,\n        status: null,\n        keyProvince: '',\n        keyCity: '',\n        keyBorough: '',\n        keyAddress: '',\n        keyDetailAddress: ''\n      },\n      // 表单校验规则\n      rules: {},\n      radio: 0,\n      // 添加新的数据属性\n      customerInfo: {\n        customerId: '',\n        applyId: ''\n      },\n      userInfoVisible: false,\n      plateNo: '',\n      carInfoVisible: false,\n      currentRow: {},\n      dispatchLoanId: null,\n      // 日常费用申请相关\n      dailyExpenseDialogVisible: false,\n      dailyExpenseForm: {\n        litigationCaseId: null,\n        expenseType: '',\n        expenseAmount: '',\n        expenseDate: '',\n        expenseDescription: '',\n        receiptUrl: '',\n        applicantId: '',\n        applicantName: '',\n        applicationTime: '',\n        approvalStatus: '0'\n      },\n      dailyExpenseRules: {\n        expenseType: [{\n          required: true,\n          message: '请选择费用类型',\n          trigger: 'change'\n        }],\n        expenseAmount: [{\n          required: true,\n          message: '请输入费用金额',\n          trigger: 'blur'\n        }, {\n          pattern: /^\\d+(\\.\\d{1,2})?$/,\n          message: '请输入正确的金额格式',\n          trigger: 'blur'\n        }],\n        expenseDate: [{\n          required: true,\n          message: '请选择费用发生日期',\n          trigger: 'change'\n        }],\n        expenseDescription: [{\n          required: true,\n          message: '请输入费用说明',\n          trigger: 'blur'\n        }]\n      },\n      receiptFileList: []\n    };\n  },\n  created: function created() {\n    this.getList();\n    this.getCarTeam();\n    this.getBankList();\n  },\n  methods: {\n    /** 查询VIEW列表 */getList: function getList() {\n      var _this = this;\n      this.loading = true;\n      (0, _petition_view.listVw_account_loan)(this.queryParams).then(function (response) {\n        _this.vw_account_loanList = response.rows;\n        _this.total = response.total;\n        _this.loading = false;\n      }).catch(function () {\n        _this.loading = false;\n      });\n    },\n    fxjCount: function fxjCount(val) {\n      this.trialForm.fxjMoney = Number(Number(val) * this.trialFormall) / 100;\n    },\n    qdCount: function qdCount(val) {\n      this.trialForm.qdMoney = Number(Number(val) * this.trialFormall) / 100;\n    },\n    gmjCount: function gmjCount(val) {\n      this.trialForm.gmjMoney = Number(Number(val) * this.trialFormall) / 100;\n    },\n    kjjCount: function kjjCount(val) {\n      this.trialForm.kjjMoney = Number(Number(val) * this.trialFormall) / 100;\n    },\n    kjczCount: function kjczCount(val) {\n      this.trialForm.kjczMoney = Number(Number(val) * this.trialFormall) / 100;\n    },\n    sbczCount: function sbczCount(val) {\n      this.trialForm.sbczMoney = Number(Number(val) * this.trialFormall) / 100;\n    },\n    handleInput3: function handleInput3(value) {\n      this.trialFormotherDebt = Number(value);\n      this.trialFormtotal = Number(this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt).toFixed(2);\n    },\n    trialSub: function trialSub() {\n      var _this2 = this;\n      var data = {\n        applyId: this.trialForm.applyId,\n        id: this.trialForm.id,\n        loanId: this.trialForm.loanId,\n        loanAmount: this.trialForm.loanAmount,\n        partnerId: this.trialForm.partnerId\n      };\n      (0, _vw_account_loan.dc_submit_order)(data).then(function (response) {\n        _this2.trialFormprincipal = response.data.principal || 0;\n        _this2.trialForm.defaultInterest = response.data.defaultInterest || 0;\n        _this2.trialForminterest = response.data.interest || 0;\n        _this2.trialFormall = response.data.btotalMoney || 0;\n        _this2.trialFormdoverdueAmount = response.data.dtotalMoney || 0;\n        _this2.trialFormliquidatedDamages = response.data.liquidatedDamages || 0;\n\n        // this.trialFormboverdueAmount = Number(\n        //   this.trialFormprincipal +\n        //     this.trialForminterest +\n        //     this.trialForm.defaultInterest\n        // ).toFixed(2);\n        // this.trialFormboverdueAmount = this.trialFormboverdueAmount\n        _this2.trialFormtotal = Number(_this2.trialFormall + _this2.trialFormdoverdueAmount + _this2.trialFormliquidatedDamages + _this2.trialFormotherDebt).toFixed(2);\n      });\n    },\n    getBankList: function getBankList() {\n      var _this3 = this;\n      (0, _vw_account_loan.get_bank_account)().then(function (response) {\n        _this3.accountList = response.rows;\n      });\n    },\n    getCarTeam: function getCarTeam() {\n      var _this4 = this;\n      this.loading = true;\n      (0, _car_team.listCar_team)({\n        status: 1\n      }).then(function (response) {\n        _this4.car_team = response.rows;\n        _this4.loading = false;\n      }).catch(function () {\n        _this4.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel: function cancel(dialog) {\n      if (dialog === 'commuteopen') {\n        this.commuteopen = false;\n      }\n      this.reset();\n    },\n    // 表单重置\n    reset: function reset() {\n      var _this$$refs$form;\n      (_this$$refs$form = this.$refs.form) === null || _this$$refs$form === void 0 || _this$$refs$form.resetFields(); // 重置表单验证\n      this.form = {};\n      this.queryParams.carStatus = null;\n      this.queryParams.isFindCar = null;\n    },\n    /** 搜索按钮操作 */handleQuery: function handleQuery() {\n      if (this.queryParams.allocationTime) {\n        this.queryParams.startTime = this.queryParams.allocationTime[0];\n        this.queryParams.endTime = this.queryParams.allocationTime[1];\n      }\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */resetQuery: function resetQuery() {\n      this.queryParams.customerName = null;\n      this.queryParams.certId = null;\n      this.queryParams.plateNo = null;\n      this.queryParams.salesman = null;\n      this.queryParams.jgName = null;\n      this.queryParams.partnerId = null;\n      this.queryParams.petitionName = null;\n      this.queryParams.followStatus = null;\n      this.queryParams.dispatchStatus = null;\n      this.queryParams.allocationTime = null;\n      this.queryParams.startTime = null;\n      this.queryParams.endTime = null;\n      this.queryParams.carStatus = null;\n      this.queryParams.isFindCar = null;\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      // This method is no longer needed as selection is removed.\n      // Keeping it for now as it might be used elsewhere or for future changes.\n    },\n    /** 修改按钮操作 */handleUpdate: function handleUpdate(row) {\n      this.open = true;\n      this.title = '修改VIEW';\n    },\n    /** 发起代偿操作 */initiate: function initiate(row) {\n      var _this5 = this;\n      //发起代偿\n      var data = {\n        loanId: row.loanId\n      };\n      (0, _vw_account_loan.loan_compensation_order)(data).then(function (response) {\n        if (response.data) {\n          _this5.trialForm = response.data;\n          _this5.trialForm.loanAmount = row.contractAmt || 0;\n          _this5.trialForm.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0;\n          _this5.trialFormprincipal = response.data.trialBalance ? response.data.trialBalance.principal : 0;\n          _this5.trialFormboverdueAmount = row.boverdueAmount || 0;\n          _this5.trialForminterest = response.data.trialBalance ? response.data.trialBalance.interest : 0;\n          _this5.trialFormall = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0;\n          _this5.trialFormdoverdueAmount = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0;\n          _this5.trialFormliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0;\n          _this5.trialFormotherDebt = response.data.otherDebt ? response.data.otherDebt : 0;\n          console.log(_this5.trialFormall, _this5.trialFormdoverdueAmount, _this5.trialFormliquidatedDamages, _this5.trialFormotherDebt);\n          _this5.trialFormtotal = Number(_this5.trialFormall + _this5.trialFormdoverdueAmount + _this5.trialFormliquidatedDamages + _this5.trialFormotherDebt).toFixed(2);\n        } else {\n          _this5.addTrial(row);\n        }\n        _this5.commuteopen = true;\n      });\n    },\n    /** 提交按钮 */submitForm: function submitForm() {\n      var _this6 = this;\n      this.$refs.form.validate(function (valid) {\n        if (valid) {\n          // 调用后端接口添加找车订单\n          (0, _car_order.addCar_order)(_this6.form).then(function (response) {\n            if (response.code === 200) {\n              (0, _elementUi.Message)({\n                message: '找车订单添加成功',\n                type: 'success',\n                duration: 3 * 1000\n              });\n              _this6.getList(); // 刷新列表\n              _this6.reset(); // 重置表单\n            } else {\n              (0, _elementUi.Message)({\n                message: '添加失败: ' + response.message,\n                type: 'error',\n                duration: 3 * 1000\n              });\n            }\n          }).catch(function (error) {\n            (0, _elementUi.Message)({\n              message: '服务器错误: ' + error.message,\n              type: 'error',\n              duration: 3 * 1000\n            });\n          });\n        } else {\n          console.log('表单验证失败');\n          return false;\n        }\n      });\n    },\n    // 添加新的方法\n    openUserInfo: function openUserInfo(customerInfo) {\n      this.customerInfo = customerInfo;\n      this.userInfoVisible = true;\n    },\n    openCarInfo: function openCarInfo(plateNo) {\n      this.plateNo = plateNo;\n      this.carInfoVisible = true;\n    },\n    // 查看催记日志\n    logView: function logView(row) {\n      this.currentRow = row;\n      this.$refs.loanReminderLog.openLogDialog();\n    },\n    openDispatchVehicleForm: function openDispatchVehicleForm(row) {\n      this.dispatchLoanId = row.loanId;\n      this.$refs.dispatchVehicleForm.openDialog();\n    },\n    // 打开日常费用申请弹窗\n    openDailyExpenseDialog: function openDailyExpenseDialog() {\n      var _this$$store$state$us, _this$$store$state$us2;\n      this.resetDailyExpenseForm();\n      this.dailyExpenseForm.applicantId = String(((_this$$store$state$us = this.$store.state.user) === null || _this$$store$state$us === void 0 ? void 0 : _this$$store$state$us.id) || '');\n      this.dailyExpenseForm.applicantName = String(((_this$$store$state$us2 = this.$store.state.user) === null || _this$$store$state$us2 === void 0 ? void 0 : _this$$store$state$us2.name) || '');\n      // 设置当前日期作为申请时间\n      var today = new Date();\n      this.dailyExpenseForm.applicationTime = today.toISOString().split('T')[0];\n      this.dailyExpenseDialogVisible = true;\n    },\n    // 重置日常费用表单\n    resetDailyExpenseForm: function resetDailyExpenseForm() {\n      var _this$$refs$dailyExpe;\n      this.dailyExpenseForm = {\n        litigationCaseId: null,\n        expenseType: '',\n        expenseAmount: '',\n        expenseDate: '',\n        expenseDescription: '',\n        receiptUrl: '',\n        applicantId: '',\n        applicantName: '',\n        applicationTime: '',\n        approvalStatus: '0'\n      };\n      this.receiptFileList = [];\n      (_this$$refs$dailyExpe = this.$refs.dailyExpenseForm) === null || _this$$refs$dailyExpe === void 0 || _this$$refs$dailyExpe.resetFields();\n    },\n    // 取消日常费用申请\n    cancelDailyExpense: function cancelDailyExpense() {\n      this.dailyExpenseDialogVisible = false;\n      this.resetDailyExpenseForm();\n    },\n    // 提交日常费用申请\n    submitDailyExpense: function submitDailyExpense() {\n      var _this7 = this;\n      this.$refs.dailyExpenseForm.validate(function (valid) {\n        if (valid) {\n          (0, _daily_expense_approval.addDaily_expense_approval)(_this7.dailyExpenseForm).then(function (res) {\n            if (res.code === 200) {\n              _this7.$message.success('日常费用申请提交成功');\n              _this7.dailyExpenseDialogVisible = false;\n              _this7.resetDailyExpenseForm();\n            } else {\n              _this7.$message.error('提交失败：' + (res.msg || '未知错误'));\n            }\n          }).catch(function (error) {\n            console.error('提交失败:', error);\n            _this7.$message.error('提交失败，请稍后重试');\n          });\n        }\n      });\n    },\n    // 处理发票上传\n    handleReceiptUpload: function handleReceiptUpload(file, fileList) {\n      this.receiptFileList = fileList;\n      // 这里可以添加文件上传到服务器的逻辑\n      // 暂时只是保存文件名\n      if (file.raw) {\n        this.dailyExpenseForm.receiptUrl = file.name;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["_petition_view", "require", "_car_team", "_car_order", "_elementUi", "_vw_account_loan", "_daily_expense_approval", "_userInfo", "_interopRequireDefault", "_carInfo", "_loanReminderLog", "_dispatchVehicleForm", "name", "components", "userInfo", "carInfo", "LoanReminderLog", "DispatchVehicleForm", "data", "loading", "showSearch", "total", "vw_account_loanList", "title", "commuteopen", "trialForm", "trialFormprincipal", "trialFormboverdueAmount", "trialForminterest", "trialFormall", "trialFormdoverdueAmount", "trialFormliquidatedDamages", "trialFormtotal", "trialFormotherDebt", "accountList", "queryParams", "pageNum", "pageSize", "customerName", "applyNo", "plateNo", "salesman", "jgName", "partnerId", "petitionName", "followStatus", "dispatchStatus", "allocationTime", "startTime", "endTime", "slippageStatus", "teamId", "garageId", "libraryStatus", "inboundTime", "outboundTime", "locatingCommission", "GPS", "keyStatus", "keyTime", "collectionMethod", "status", "key<PERSON><PERSON>ince", "keyCity", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "keyDetailAddress", "car<PERSON>tatus", "isFindCar", "bankList", "value", "label", "followUpList", "dispatchList", "carStatusList", "form", "mobilePhone", "contractId", "customerId", "carDetailAddress", "gpsStatus", "followUpType", "rules", "radio", "customerInfo", "applyId", "userInfoVisible", "carInfoVisible", "currentRow", "dispatchLoanId", "dailyExpenseDialogVisible", "dailyExpenseForm", "litigationCaseId", "expenseType", "expenseAmount", "expenseDate", "expenseDescription", "receiptUrl", "applicantId", "applicantName", "applicationTime", "approvalStatus", "dailyExpenseRules", "required", "message", "trigger", "pattern", "receiptFileList", "created", "getList", "getCarTeam", "getBankList", "methods", "_this", "listVw_account_loan", "then", "response", "rows", "catch", "fxjCount", "val", "fxjMoney", "Number", "qdCount", "qdMoney", "gmjCount", "gmjMoney", "kjjCount", "kjjMoney", "kjczCount", "kjczMoney", "sbczCount", "sbczMoney", "handleInput3", "toFixed", "trialSub", "_this2", "id", "loanId", "loanAmount", "dc_submit_order", "principal", "defaultInterest", "interest", "btotalMoney", "dtotal<PERSON><PERSON>", "liquidatedDamages", "_this3", "get_bank_account", "_this4", "listCar_team", "car_team", "cancel", "dialog", "reset", "_this$$refs$form", "$refs", "resetFields", "handleQuery", "reset<PERSON><PERSON>y", "certId", "handleSelectionChange", "selection", "handleUpdate", "row", "open", "initiate", "_this5", "loan_compensation_order", "contractAmt", "trialBalance", "boverdueAmount", "otherDebt", "console", "log", "addTrial", "submitForm", "_this6", "validate", "valid", "addCar_order", "code", "Message", "type", "duration", "error", "openUserInfo", "openCarInfo", "logView", "loanReminderLog", "openLogDialog", "openDispatchVehicleForm", "dispatchVehicleForm", "openDialog", "openDailyExpenseDialog", "_this$$store$state$us", "_this$$store$state$us2", "resetDailyExpenseForm", "String", "$store", "state", "user", "today", "Date", "toISOString", "split", "_this$$refs$dailyExpe", "cancelDailyExpense", "submitDailyExpense", "_this7", "addDaily_expense_approval", "res", "$message", "success", "msg", "handleReceiptUpload", "file", "fileList", "raw"], "sources": ["src/views/petition_view/petition_view/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"贷款人账户、姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"certId\">\r\n        <el-input v-model=\"queryParams.certId\" placeholder=\"贷款人身份证号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"plateNo\">\r\n        <el-input v-model=\"queryParams.plateNo\" placeholder=\"车牌号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"salesman\">\r\n        <el-input v-model=\"queryParams.salesman\" placeholder=\"业务员姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input v-model=\"queryParams.jgName\" placeholder=\"录单渠道名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"partnerId\">\r\n        <el-select v-model=\"queryParams.partnerId\" placeholder=\"放款银行\" clearable>\r\n          <el-option v-for=\"dict in bankList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"petitionName\">\r\n        <el-input v-model=\"queryParams.petitionName\" placeholder=\"上访员姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"followStatus\">\r\n        <el-select v-model=\"queryParams.followStatus\" placeholder=\"跟催类型\" clearable>\r\n          <el-option v-for=\"dict in followUpList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"dispatchStatus\">\r\n        <el-select v-model=\"queryParams.dispatchStatus\" placeholder=\"派单状态\" clearable>\r\n          <el-option v-for=\"dict in dispatchList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"指派时间\">\r\n        <el-date-picker\r\n          v-model=\"queryParams.allocationTime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"车辆状态\">\r\n        <el-select v-model=\"queryParams.carStatus\" placeholder=\"车辆状态\" clearable>\r\n          <el-option v-for=\"dict in carStatusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否派单找车\">\r\n        <el-select v-model=\"queryParams.isFindCar\" placeholder=\"是否派单找车\" clearable>\r\n          <el-option label=\"未派单\" :value=\"0\" />\r\n          <el-option label=\"已派单\" :value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        <el-button type=\"success\" icon=\"el-icon-document\" size=\"mini\" @click=\"openDailyExpenseDialog\" v-hasPermi=\"['daily_expense_approval:daily_expense_approval:add']\">提交日常费用</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"vw_account_loanList\">\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"55\" fixed=\"left\" />\r\n      <el-table-column label=\"逾期状态\" align=\"center\" prop=\"slippageStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span>\r\n            {{\r\n              scope.row.slippageStatus == 1\r\n                ? '提醒'\r\n                : scope.row.slippageStatus == 2\r\n                  ? '电催'\r\n                  : scope.row.slippageStatus == 3\r\n                    ? '上访'\r\n                    : scope.row.slippageStatus == 4\r\n                      ? '逾期30-60'\r\n                      : '逾期60+'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"还款状态\" align=\"center\" prop=\"repaymentStatus\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span>\r\n            {{\r\n              scope.row.repaymentStatus == 1\r\n                ? '还款中'\r\n                : scope.row.repaymentStatus == 2\r\n                  ? '已完结'\r\n                  : scope.row.repaymentStatus == 3\r\n                    ? '提前结清'\r\n                    : scope.row.repaymentStatus == 4\r\n                      ? '逾期催回结清'\r\n                      : scope.row.repaymentStatus == 5\r\n                        ? '逾期减免结清'\r\n                        : scope.row.repaymentStatus == 6\r\n                          ? '逾期未还款'\r\n                          : scope.row.repaymentStatus == 7\r\n                            ? '逾期还款中'\r\n                            : scope.row.repaymentStatus == 8\r\n                              ? '代偿未还款'\r\n                              : scope.row.repaymentStatus == 9\r\n                                ? '代偿还款中'\r\n                                : scope.row.repaymentStatus == 10\r\n                                  ? '代偿减免结清'\r\n                                  : scope.row.repaymentStatus == 11\r\n                                    ? '代偿全额结清'\r\n                                    : '未知状态'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"贷款人\" align=\"center\" prop=\"customerName\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"openUserInfo({ customerId: scope.row.customerId, applyId: scope.row.applyId })\">\r\n            {{ scope.row.customerName }}\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\" />\r\n      <el-table-column label=\"业务员\" align=\"center\" prop=\"nickName\" />\r\n      <el-table-column label=\"车牌号码\" align=\"center\" prop=\"plateNo\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"openCarInfo(scope.row.plateNo)\">{{ scope.row.plateNo }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"车辆状态\" align=\"center\" prop=\"carStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.carStatus != null\">\r\n            {{\r\n              (carStatusList.find(item => item.value === String(scope.row.carStatus)) || {}).label || ''\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"派车团队\" align=\"center\" prop=\"carTeamName\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.carTeamName\">{{ scope.row.carTeamName }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"位置\" align=\"center\" prop=\"carDetailAddress\" />\r\n      <el-table-column label=\"GPS状态\" align=\"center\" prop=\"gpsStatus\" />\r\n      <el-table-column label=\"派车单状态\" align=\"center\" prop=\"loanStatus\" width=\"130\" />\r\n      <el-table-column label=\"放款银行\" align=\"center\" prop=\"orgName\" />\r\n      <el-table-column label=\"逾期天数\" align=\"center\" prop=\"boverdueDays\" width=\"130\" />\r\n      <el-table-column label=\"首期逾期金额\" align=\"center\" prop=\"foverdueAmount\" width=\"130\" />\r\n      <el-table-column label=\"银行逾期金额\" align=\"center\" prop=\"boverdueAmount\" width=\"130\" />\r\n      <el-table-column label=\"代扣逾期金额\" align=\"center\" prop=\"doverdueAmount\" width=\"130\" />\r\n      <el-table-column label=\"银行结清金额\" align=\"center\" prop=\"bSettleAmount\" width=\"130\" />\r\n      <el-table-column label=\"代扣结清金额\" align=\"center\" prop=\"dSettleAmount\" width=\"130\" />\r\n      <el-table-column label=\"上访员\" align=\"center\" prop=\"petitionUser\" width=\"130\" />\r\n      <el-table-column label=\"跟催类型\" align=\"center\" prop=\"urgeType\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.urgeType != null\">\r\n            {{ scope.row.urgeType == 1 ? '继续联系' : scope.row.urgeType == 2 ? '约定还款' : '无法跟进' }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"催记日期\" align=\"center\" prop=\"assignTime\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.assignTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"分配时间\" align=\"center\" prop=\"petitionTime\" width=\"130\" />\r\n      <el-table-column label=\"下次跟进时间\" align=\"center\" prop=\"nextFollowTime\" width=\"130\" />\r\n      <el-table-column label=\"预扣款时间\" align=\"center\" prop=\"preDeductionTime\" width=\"130\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-popover placement=\"left\" trigger=\"click\" popper-class=\"custom-popover\">\r\n            <div class=\"operation-buttons\">\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"initiate(scope.row)\"\r\n                v-hasPermi=\"['vw_account_loan:vw_account_loan:edit']\">\r\n                发起代偿\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openDispatchVehicleForm(scope.row)\"\r\n                v-hasPermi=\"['vw_account_loan:vw_account_loan:edit']\">\r\n                发起找车\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"logView(scope.row)\"\r\n                v-hasPermi=\"['vw_account_loan:vw_account_loan:edit']\">\r\n                查看催记\r\n              </el-button>\r\n            </div>\r\n            <el-button slot=\"reference\" size=\"mini\" type=\"text\">更多</el-button>\r\n          </el-popover>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n\r\n    <!-- 发起代偿对话框 -->\r\n    <el-dialog :close-on-click-modal=\"false\" class=\"dialogBox\" title=\"发起代偿\" :visible.sync=\"commuteopen\" width=\"900px\" append-to-body>\r\n      <div class=\"settle_money\" @click=\"trialSub\">发起试算</div>\r\n      <el-form ref=\"trialForm\" :model=\"trialForm\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"贷款人\" prop=\"customerName\">\r\n              <el-input v-model=\"trialForm.customerName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"出单渠道\" prop=\"orgName\">\r\n              <el-input v-model=\"trialForm.orgName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款银行\" prop=\"bank\">\r\n              <el-input v-model=\"trialForm.bank\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款金额\" prop=\"loanAmount\">\r\n              <el-input v-model=\"trialForm.loanAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"剩余本金\">\r\n              <el-input v-model=\"trialFormprincipal\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行逾期金额\">\r\n              <el-input v-model=\"trialFormboverdueAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行利息\">\r\n              <el-input v-model=\"trialForminterest\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"代偿总金额\">\r\n              <el-input v-model=\"trialFormall\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"风险金划比例\" prop=\"fxjProportion\">\r\n              <el-input v-model=\"trialForm.fxjProportion\" @input=\"fxjCount\">\r\n                <template #append>%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"风险金划金额\" prop=\"fxjMoney\">\r\n              <el-input v-model=\"trialForm.fxjMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"fxjAccount\">\r\n              <el-select v-model=\"trialForm.fxjAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"渠道转入比例\" prop=\"qdProportion\">\r\n              <el-input v-model=\"trialForm.qdProportion\" @input=\"qdCount\">\r\n                <template #append>%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"渠道转入金额\" prop=\"qdMoney\">\r\n              <el-input v-model=\"trialForm.qdMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"qdAccount\">\r\n              <el-select v-model=\"trialForm.qdAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"广明借比例\" prop=\"gmjProportion\">\r\n              <el-input v-model=\"trialForm.gmjProportion\" @input=\"gmjCount\">\r\n                <template #append>%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"广明借额\" prop=\"gmjMoney\">\r\n              <el-input v-model=\"trialForm.gmjMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"gmjAccount\">\r\n              <el-select v-model=\"trialForm.gmjAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技借比例\" prop=\"kjczProportion\">\r\n              <el-input v-model=\"trialForm.kjczProportion\" @input=\"kjjCount\">\r\n                <template #append>%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技借额\" prop=\"kjjMoney\">\r\n              <el-input v-model=\"trialForm.kjjMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"kjjAccount\">\r\n              <el-select v-model=\"trialForm.kjjAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技出资比例\" prop=\"kjczProportion\">\r\n              <el-input v-model=\"trialForm.kjczProportion\" @input=\"kjczCount\">\r\n                <template #append>%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技出资金额\" prop=\"kjczMoney\">\r\n              <el-input v-model=\"trialForm.kjczMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"kjczAccount\">\r\n              <el-select v-model=\"trialForm.kjczAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"守邦出资比例\" prop=\"sbczProportion\">\r\n              <el-input v-model=\"trialForm.sbczProportion\" @input=\"sbczCount\">\r\n                <template #append>%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"守邦出资金额\" prop=\"sbczMoney\">\r\n              <el-input v-model=\"trialForm.sbczMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"sbczAccount\">\r\n              <el-select v-model=\"trialForm.sbczAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"代扣剩余未还金额\">\r\n              <el-input v-model=\"trialFormdoverdueAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"违约金\">\r\n              <el-input v-model=\"trialFormliquidatedDamages\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"登记其他欠款金额\" prop=\"otherDebt\">\r\n              <el-input type=\"number\" @input=\"handleInput3\" v-model=\"trialFormotherDebt\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"总账款\">\r\n              <el-input v-model=\"trialFormtotal\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button v-if=\"trialForm.examineStatus != 1\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel('commuteopen')\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 贷款人信息对话框 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n    <!-- 车辆信息 -->\r\n    <carInfo ref=\"carInfo\" :visible.sync=\"carInfoVisible\" title=\"车辆信息\" :plateNo=\"plateNo\" permission=\"2\" />\r\n\r\n    <!-- 催记日志组件 -->\r\n    <loan-reminder-log ref=\"loanReminderLog\" :loan-id=\"currentRow.loanId\" />\r\n    <DispatchVehicleForm ref=\"dispatchVehicleForm\" :loanId=\"dispatchLoanId\" />\r\n\r\n    <!-- 日常费用申请弹窗 -->\r\n    <el-dialog title=\"提交日常费用\" :visible.sync=\"dailyExpenseDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"dailyExpenseForm\" :model=\"dailyExpenseForm\" :rules=\"dailyExpenseRules\" label-width=\"120px\">\r\n        <el-form-item label=\"费用类型\" prop=\"expenseType\">\r\n          <el-select v-model=\"dailyExpenseForm.expenseType\" placeholder=\"请选择费用类型\" style=\"width: 100%\">\r\n            <el-option label=\"油费\" value=\"oil_fee\" />\r\n            <el-option label=\"路费\" value=\"road_fee\" />\r\n            <el-option label=\"餐费\" value=\"meal_fee\" />\r\n            <el-option label=\"住宿费\" value=\"accommodation_fee\" />\r\n            <el-option label=\"交通费\" value=\"transport_fee\" />\r\n            <el-option label=\"停车费\" value=\"parking_fee\" />\r\n            <el-option label=\"通讯费\" value=\"communication_fee\" />\r\n            <el-option label=\"其他\" value=\"other\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"费用金额\" prop=\"expenseAmount\">\r\n          <el-input v-model=\"dailyExpenseForm.expenseAmount\" placeholder=\"请输入费用金额\">\r\n            <template slot=\"prepend\">￥</template>\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"费用发生日期\" prop=\"expenseDate\">\r\n          <el-date-picker\r\n            v-model=\"dailyExpenseForm.expenseDate\"\r\n            type=\"date\"\r\n            placeholder=\"请选择费用发生日期\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            style=\"width: 100%\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"费用说明\" prop=\"expenseDescription\">\r\n          <el-input\r\n            v-model=\"dailyExpenseForm.expenseDescription\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入费用说明\">\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"发票/凭证\">\r\n          <el-upload\r\n            class=\"upload-demo\"\r\n            action=\"#\"\r\n            :auto-upload=\"false\"\r\n            :on-change=\"handleReceiptUpload\"\r\n            :file-list=\"receiptFileList\"\r\n            list-type=\"picture\">\r\n            <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过2MB</div>\r\n          </el-upload>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitDailyExpense\">确 定</el-button>\r\n        <el-button @click=\"cancelDailyExpense\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listVw_account_loan } from '@/api/petition_view/petition_view'\r\nimport { listCar_team } from '@/api/car_team/car_team'\r\nimport { addCar_order } from '@/api/car_order/car_order'\r\nimport { Message } from 'element-ui'\r\nimport { get_bank_account, dc_submit_order, loan_compensation_order } from '@/api/vw_account_loan/vw_account_loan'\r\nimport { addDaily_expense_approval } from '@/api/daily_expense_approval/daily_expense_approval'\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport carInfo from '@/layout/components/Dialog/carInfo.vue'\r\nimport LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'\r\nimport DispatchVehicleForm from '@/layout/components/Dialog/dispatchVehicleForm.vue'\r\n\r\nexport default {\r\n  name: 'Vw_account_loan',\r\n  components: {\r\n    userInfo,\r\n    carInfo,\r\n    LoanReminderLog,\r\n    DispatchVehicleForm,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // VIEW表格数据\r\n      vw_account_loanList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否弹出发起代偿对话框\r\n      commuteopen: false,\r\n      trialForm: {},\r\n      trialFormprincipal: 0,\r\n      trialFormboverdueAmount: 0,\r\n      trialForminterest: 0,\r\n      trialFormall: 0,\r\n      trialFormdoverdueAmount: 0,\r\n      trialFormliquidatedDamages: 0,\r\n      trialFormtotal: 0,\r\n      trialFormotherDebt: 0,\r\n      accountList: [], //银行账户列表\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 15,\r\n        customerName: null,\r\n        applyNo: null, // 更新为后端字段\r\n        plateNo: null,\r\n        salesman: null,\r\n        jgName: null,\r\n        partnerId: null,\r\n        petitionName: null,\r\n        followStatus: null,\r\n        dispatchStatus: null,\r\n        allocationTime: null,\r\n        startTime: null,\r\n        endTime: null,\r\n        slippageStatus: 3,\r\n        teamId: null,\r\n        garageId: null,\r\n        libraryStatus: null,\r\n        inboundTime: null,\r\n        outboundTime: null,\r\n        locatingCommission: null,\r\n        GPS: null,\r\n        keyStatus: null,\r\n        keyTime: null,\r\n        collectionMethod: null,\r\n        status: null,\r\n        keyProvince: null,\r\n        keyCity: null,\r\n        keyBorough: null,\r\n        keyAddress: null,\r\n        keyDetailAddress: null,\r\n        carStatus: null,\r\n        isFindCar: null,\r\n      },\r\n      bankList: [\r\n        { value: 'EO00000010', label: '苏银金租' },\r\n        { value: 'IO00000006', label: '浙商银行' },\r\n        { value: 'IO00000007', label: '中关村银行' },\r\n        { value: 'IO00000008', label: '蓝海银行' },\r\n        { value: 'IO00000009', label: '华瑞银行' },\r\n        { value: 'IO00000010', label: '皖新租赁' },\r\n      ],\r\n      followUpList: [\r\n        { label: '无法跟进', value: 1 },\r\n        { label: '约定还款', value: 2 },\r\n        { label: '继续联系', value: 3 },\r\n      ],\r\n      dispatchList: [\r\n        { label: '待派单', value: 1 },\r\n        { label: '找车中', value: 2 },\r\n        { label: '已入库', value: 3 },\r\n        { label: '未派单', value: 4 },\r\n        { label: '已撤销', value: 5 },\r\n      ],\r\n      carStatusList: [\r\n        { label: '省内正常行驶', value: '1' },\r\n        { label: '省外正常行驶', value: '2' },\r\n        { label: '抵押', value: '3' },\r\n        { label: '疑似抵押', value: '4' },\r\n        { label: '疑似黑车', value: '5' },\r\n        { label: '已入库', value: '6' },\r\n        { label: '车在法院', value: '7' },\r\n        { label: '已法拍', value: '8' },\r\n        { label: '协商卖车', value: '9' },\r\n      ],\r\n      // 表单参数\r\n      form: {\r\n        customerName: '',\r\n        mobilePhone: '',\r\n        contractId: '',\r\n        customerId: '',\r\n        plateNo: '',\r\n        carStatus: '',\r\n        carDetailAddress: '',\r\n        gpsStatus: '',\r\n        followUpType: '',\r\n        applyNo: '',\r\n        teamId: null,\r\n        garageId: null,\r\n        libraryStatus: null,\r\n        inboundTime: '',\r\n        outboundTime: '',\r\n        locatingCommission: null,\r\n        keyStatus: null,\r\n        keyTime: '',\r\n        collectionMethod: null,\r\n        status: null,\r\n        keyProvince: '',\r\n        keyCity: '',\r\n        keyBorough: '',\r\n        keyAddress: '',\r\n        keyDetailAddress: '',\r\n      },\r\n      // 表单校验规则\r\n      rules: {},\r\n      radio: 0,\r\n      // 添加新的数据属性\r\n      customerInfo: { customerId: '', applyId: '' },\r\n      userInfoVisible: false,\r\n      plateNo: '',\r\n      carInfoVisible: false,\r\n      currentRow: {},\r\n      dispatchLoanId: null,\r\n      // 日常费用申请相关\r\n      dailyExpenseDialogVisible: false,\r\n      dailyExpenseForm: {\r\n        litigationCaseId: null,\r\n        expenseType: '',\r\n        expenseAmount: '',\r\n        expenseDate: '',\r\n        expenseDescription: '',\r\n        receiptUrl: '',\r\n        applicantId: '',\r\n        applicantName: '',\r\n        applicationTime: '',\r\n        approvalStatus: '0'\r\n      },\r\n      dailyExpenseRules: {\r\n        expenseType: [{ required: true, message: '请选择费用类型', trigger: 'change' }],\r\n        expenseAmount: [\r\n          { required: true, message: '请输入费用金额', trigger: 'blur' },\r\n          { pattern: /^\\d+(\\.\\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }\r\n        ],\r\n        expenseDate: [{ required: true, message: '请选择费用发生日期', trigger: 'change' }],\r\n        expenseDescription: [{ required: true, message: '请输入费用说明', trigger: 'blur' }]\r\n      },\r\n      receiptFileList: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getCarTeam()\r\n    this.getBankList()\r\n  },\r\n  methods: {\r\n    /** 查询VIEW列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listVw_account_loan(this.queryParams)\r\n        .then(response => {\r\n          this.vw_account_loanList = response.rows\r\n          this.total = response.total\r\n          this.loading = false\r\n        })\r\n        .catch(() => {\r\n          this.loading = false\r\n        })\r\n    },\r\n    fxjCount(val) {\r\n      this.trialForm.fxjMoney = Number(Number(val) * this.trialFormall) / 100\r\n    },\r\n    qdCount(val) {\r\n      this.trialForm.qdMoney = Number(Number(val) * this.trialFormall) / 100\r\n    },\r\n    gmjCount(val) {\r\n      this.trialForm.gmjMoney = Number(Number(val) * this.trialFormall) / 100\r\n    },\r\n    kjjCount(val) {\r\n      this.trialForm.kjjMoney = Number(Number(val) * this.trialFormall) / 100\r\n    },\r\n    kjczCount(val) {\r\n      this.trialForm.kjczMoney = Number(Number(val) * this.trialFormall) / 100\r\n    },\r\n    sbczCount(val) {\r\n      this.trialForm.sbczMoney = Number(Number(val) * this.trialFormall) / 100\r\n    },\r\n    handleInput3(value) {\r\n      this.trialFormotherDebt = Number(value)\r\n      this.trialFormtotal = Number(\r\n        this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt\r\n      ).toFixed(2)\r\n    },\r\n    trialSub() {\r\n      var data = {\r\n        applyId: this.trialForm.applyId,\r\n        id: this.trialForm.id,\r\n        loanId: this.trialForm.loanId,\r\n        loanAmount: this.trialForm.loanAmount,\r\n        partnerId: this.trialForm.partnerId,\r\n      }\r\n      dc_submit_order(data).then(response => {\r\n        this.trialFormprincipal = response.data.principal || 0\r\n        this.trialForm.defaultInterest = response.data.defaultInterest || 0\r\n        this.trialForminterest = response.data.interest || 0\r\n        this.trialFormall = response.data.btotalMoney || 0\r\n        this.trialFormdoverdueAmount = response.data.dtotalMoney || 0\r\n        this.trialFormliquidatedDamages = response.data.liquidatedDamages || 0\r\n\r\n        // this.trialFormboverdueAmount = Number(\r\n        //   this.trialFormprincipal +\r\n        //     this.trialForminterest +\r\n        //     this.trialForm.defaultInterest\r\n        // ).toFixed(2);\r\n        // this.trialFormboverdueAmount = this.trialFormboverdueAmount\r\n        this.trialFormtotal = Number(\r\n          this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt\r\n        ).toFixed(2)\r\n      })\r\n    },\r\n    getBankList() {\r\n      get_bank_account().then(response => {\r\n        this.accountList = response.rows\r\n      })\r\n    },\r\n    getCarTeam() {\r\n      this.loading = true\r\n      listCar_team({ status: 1 })\r\n        .then(response => {\r\n          this.car_team = response.rows\r\n          this.loading = false\r\n        })\r\n        .catch(() => {\r\n          this.loading = false\r\n        })\r\n    },\r\n    // 取消按钮\r\n    cancel(dialog) {\r\n      if (dialog === 'commuteopen') {\r\n        this.commuteopen = false\r\n      }\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.$refs.form?.resetFields() // 重置表单验证\r\n      this.form = {}\r\n      this.queryParams.carStatus = null\r\n      this.queryParams.isFindCar = null\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      if (this.queryParams.allocationTime) {\r\n        this.queryParams.startTime = this.queryParams.allocationTime[0]\r\n        this.queryParams.endTime = this.queryParams.allocationTime[1]\r\n      }\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams.customerName = null\r\n      this.queryParams.certId = null\r\n      this.queryParams.plateNo = null\r\n      this.queryParams.salesman = null\r\n      this.queryParams.jgName = null\r\n      this.queryParams.partnerId = null\r\n      this.queryParams.petitionName = null\r\n      this.queryParams.followStatus = null\r\n      this.queryParams.dispatchStatus = null\r\n      this.queryParams.allocationTime = null\r\n      this.queryParams.startTime = null\r\n      this.queryParams.endTime = null\r\n      this.queryParams.carStatus = null\r\n      this.queryParams.isFindCar = null\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      // This method is no longer needed as selection is removed.\r\n      // Keeping it for now as it might be used elsewhere or for future changes.\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.open = true\r\n      this.title = '修改VIEW'\r\n    },\r\n    /** 发起代偿操作 */\r\n    initiate(row) {\r\n      //发起代偿\r\n      var data = {\r\n        loanId: row.loanId,\r\n      }\r\n      loan_compensation_order(data).then(response => {\r\n        if (response.data) {\r\n          this.trialForm = response.data\r\n          this.trialForm.loanAmount = row.contractAmt || 0\r\n          this.trialForm.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n          this.trialFormprincipal = response.data.trialBalance ? response.data.trialBalance.principal : 0\r\n          this.trialFormboverdueAmount = row.boverdueAmount || 0\r\n          this.trialForminterest = response.data.trialBalance ? response.data.trialBalance.interest : 0\r\n          this.trialFormall = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0\r\n          this.trialFormdoverdueAmount = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n          this.trialFormliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0\r\n          this.trialFormotherDebt = response.data.otherDebt ? response.data.otherDebt : 0\r\n          console.log(this.trialFormall, this.trialFormdoverdueAmount, this.trialFormliquidatedDamages, this.trialFormotherDebt)\r\n          this.trialFormtotal = Number(\r\n            this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt\r\n          ).toFixed(2)\r\n        } else {\r\n          this.addTrial(row)\r\n        }\r\n        this.commuteopen = true\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          // 调用后端接口添加找车订单\r\n          addCar_order(this.form)\r\n            .then(response => {\r\n              if (response.code === 200) {\r\n                Message({\r\n                  message: '找车订单添加成功',\r\n                  type: 'success',\r\n                  duration: 3 * 1000,\r\n                })\r\n                this.getList() // 刷新列表\r\n                this.reset() // 重置表单\r\n              } else {\r\n                Message({\r\n                  message: '添加失败: ' + response.message,\r\n                  type: 'error',\r\n                  duration: 3 * 1000,\r\n                })\r\n              }\r\n            })\r\n            .catch(error => {\r\n              Message({\r\n                message: '服务器错误: ' + error.message,\r\n                type: 'error',\r\n                duration: 3 * 1000,\r\n              })\r\n            })\r\n        } else {\r\n          console.log('表单验证失败')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 添加新的方法\r\n    openUserInfo(customerInfo) {\r\n      this.customerInfo = customerInfo\r\n      this.userInfoVisible = true\r\n    },\r\n    openCarInfo(plateNo) {\r\n      this.plateNo = plateNo\r\n      this.carInfoVisible = true\r\n    },\r\n    // 查看催记日志\r\n    logView(row) {\r\n      this.currentRow = row\r\n      this.$refs.loanReminderLog.openLogDialog()\r\n    },\r\n    openDispatchVehicleForm(row) {\r\n      this.dispatchLoanId = row.loanId\r\n      this.$refs.dispatchVehicleForm.openDialog()\r\n    },\r\n    // 打开日常费用申请弹窗\r\n    openDailyExpenseDialog() {\r\n      this.resetDailyExpenseForm()\r\n      this.dailyExpenseForm.applicantId = String(this.$store.state.user?.id || '')\r\n      this.dailyExpenseForm.applicantName = String(this.$store.state.user?.name || '')\r\n      // 设置当前日期作为申请时间\r\n      const today = new Date()\r\n      this.dailyExpenseForm.applicationTime = today.toISOString().split('T')[0]\r\n      this.dailyExpenseDialogVisible = true\r\n    },\r\n    // 重置日常费用表单\r\n    resetDailyExpenseForm() {\r\n      this.dailyExpenseForm = {\r\n        litigationCaseId: null,\r\n        expenseType: '',\r\n        expenseAmount: '',\r\n        expenseDate: '',\r\n        expenseDescription: '',\r\n        receiptUrl: '',\r\n        applicantId: '',\r\n        applicantName: '',\r\n        applicationTime: '',\r\n        approvalStatus: '0'\r\n      }\r\n      this.receiptFileList = []\r\n      this.$refs.dailyExpenseForm?.resetFields()\r\n    },\r\n    // 取消日常费用申请\r\n    cancelDailyExpense() {\r\n      this.dailyExpenseDialogVisible = false\r\n      this.resetDailyExpenseForm()\r\n    },\r\n    // 提交日常费用申请\r\n    submitDailyExpense() {\r\n      this.$refs.dailyExpenseForm.validate((valid) => {\r\n        if (valid) {\r\n          addDaily_expense_approval(this.dailyExpenseForm).then(res => {\r\n            if (res.code === 200) {\r\n              this.$message.success('日常费用申请提交成功')\r\n              this.dailyExpenseDialogVisible = false\r\n              this.resetDailyExpenseForm()\r\n            } else {\r\n              this.$message.error('提交失败：' + (res.msg || '未知错误'))\r\n            }\r\n          }).catch(error => {\r\n            console.error('提交失败:', error)\r\n            this.$message.error('提交失败，请稍后重试')\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 处理发票上传\r\n    handleReceiptUpload(file, fileList) {\r\n      this.receiptFileList = fileList\r\n      // 这里可以添加文件上传到服务器的逻辑\r\n      // 暂时只是保存文件名\r\n      if (file.raw) {\r\n        this.dailyExpenseForm.receiptUrl = file.name\r\n      }\r\n    }\r\n  },\r\n}\r\n</script>\r\n<style>\r\n.dialogBox .el-form-item__label {\r\n  width: 100px !important;\r\n}\r\n\r\n.dialogBox .el-form-item__content {\r\n  margin-left: 100px !important;\r\n}\r\n\r\n.warnBox {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.warnImg {\r\n  width: 20px;\r\n  height: 20px;\r\n  margin: 0 5px 0 10px;\r\n}\r\n\r\n.settle_money {\r\n  background-color: #1890ff;\r\n  color: #fff;\r\n  border-radius: 5px;\r\n  display: inline-block;\r\n  padding: 5px 10px;\r\n  box-sizing: border-box;\r\n  margin-bottom: 10px;\r\n}\r\n/* 操作按钮容器 */\r\n.operation-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n  width: 90px;\r\n  padding: 4px 8px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.operation-btn {\r\n  width: 74px !important;\r\n  height: 26px !important;\r\n  margin: 0 !important;\r\n  padding: 0 2px !important;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  text-align: center;\r\n  line-height: 26px;\r\n}\r\n\r\n/* 按钮悬停效果 */\r\n.operation-btn:hover {\r\n  background-color: #f5f7fa;\r\n  color: #409eff;\r\n}\r\n</style>\r\n<style>\r\n.custom-popover {\r\n  width: 116px !important;\r\n  min-width: 116px !important;\r\n  max-width: 116px !important;\r\n  box-sizing: border-box !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAieA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,gBAAA,GAAAJ,OAAA;AACA,IAAAK,uBAAA,GAAAL,OAAA;AACA,IAAAM,SAAA,GAAAC,sBAAA,CAAAP,OAAA;AACA,IAAAQ,QAAA,GAAAD,sBAAA,CAAAP,OAAA;AACA,IAAAS,gBAAA,GAAAF,sBAAA,CAAAP,OAAA;AACA,IAAAU,oBAAA,GAAAH,sBAAA,CAAAP,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAW,IAAA;EACAC,UAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,OAAA,EAAAA,gBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,mBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,mBAAA;MACA;MACAC,KAAA;MACA;MACAC,WAAA;MACAC,SAAA;MACAC,kBAAA;MACAC,uBAAA;MACAC,iBAAA;MACAC,YAAA;MACAC,uBAAA;MACAC,0BAAA;MACAC,cAAA;MACAC,kBAAA;MACAC,WAAA;MAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA;QACAC,OAAA;QAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,SAAA;QACAC,YAAA;QACAC,YAAA;QACAC,cAAA;QACAC,cAAA;QACAC,SAAA;QACAC,OAAA;QACAC,cAAA;QACAC,MAAA;QACAC,QAAA;QACAC,aAAA;QACAC,WAAA;QACAC,YAAA;QACAC,kBAAA;QACAC,GAAA;QACAC,SAAA;QACAC,OAAA;QACAC,gBAAA;QACAC,MAAA;QACAC,WAAA;QACAC,OAAA;QACAC,UAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,SAAA;QACAC,SAAA;MACA;MACAC,QAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,YAAA,GACA;QAAAD,KAAA;QAAAD,KAAA;MAAA,GACA;QAAAC,KAAA;QAAAD,KAAA;MAAA,GACA;QAAAC,KAAA;QAAAD,KAAA;MAAA,EACA;MACAG,YAAA,GACA;QAAAF,KAAA;QAAAD,KAAA;MAAA,GACA;QAAAC,KAAA;QAAAD,KAAA;MAAA,GACA;QAAAC,KAAA;QAAAD,KAAA;MAAA,GACA;QAAAC,KAAA;QAAAD,KAAA;MAAA,GACA;QAAAC,KAAA;QAAAD,KAAA;MAAA,EACA;MACAI,aAAA,GACA;QAAAH,KAAA;QAAAD,KAAA;MAAA,GACA;QAAAC,KAAA;QAAAD,KAAA;MAAA,GACA;QAAAC,KAAA;QAAAD,KAAA;MAAA,GACA;QAAAC,KAAA;QAAAD,KAAA;MAAA,GACA;QAAAC,KAAA;QAAAD,KAAA;MAAA,GACA;QAAAC,KAAA;QAAAD,KAAA;MAAA,GACA;QAAAC,KAAA;QAAAD,KAAA;MAAA,GACA;QAAAC,KAAA;QAAAD,KAAA;MAAA,GACA;QAAAC,KAAA;QAAAD,KAAA;MAAA,EACA;MACA;MACAK,IAAA;QACArC,YAAA;QACAsC,WAAA;QACAC,UAAA;QACAC,UAAA;QACAtC,OAAA;QACA2B,SAAA;QACAY,gBAAA;QACAC,SAAA;QACAC,YAAA;QACA1C,OAAA;QACAY,MAAA;QACAC,QAAA;QACAC,aAAA;QACAC,WAAA;QACAC,YAAA;QACAC,kBAAA;QACAE,SAAA;QACAC,OAAA;QACAC,gBAAA;QACAC,MAAA;QACAC,WAAA;QACAC,OAAA;QACAC,UAAA;QACAC,UAAA;QACAC,gBAAA;MACA;MACA;MACAgB,KAAA;MACAC,KAAA;MACA;MACAC,YAAA;QAAAN,UAAA;QAAAO,OAAA;MAAA;MACAC,eAAA;MACA9C,OAAA;MACA+C,cAAA;MACAC,UAAA;MACAC,cAAA;MACA;MACAC,yBAAA;MACAC,gBAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,WAAA;QACAC,kBAAA;QACAC,UAAA;QACAC,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,cAAA;MACA;MACAC,iBAAA;QACAT,WAAA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAX,aAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,OAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,WAAA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAT,kBAAA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAE,eAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,UAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,eACAH,OAAA,WAAAA,QAAA;MAAA,IAAAI,KAAA;MACA,KAAA9F,OAAA;MACA,IAAA+F,kCAAA,OAAA/E,WAAA,EACAgF,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA3F,mBAAA,GAAA8F,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAA5F,KAAA,GAAA+F,QAAA,CAAA/F,KAAA;QACA4F,KAAA,CAAA9F,OAAA;MACA,GACAmG,KAAA;QACAL,KAAA,CAAA9F,OAAA;MACA;IACA;IACAoG,QAAA,WAAAA,SAAAC,GAAA;MACA,KAAA/F,SAAA,CAAAgG,QAAA,GAAAC,MAAA,CAAAA,MAAA,CAAAF,GAAA,SAAA3F,YAAA;IACA;IACA8F,OAAA,WAAAA,QAAAH,GAAA;MACA,KAAA/F,SAAA,CAAAmG,OAAA,GAAAF,MAAA,CAAAA,MAAA,CAAAF,GAAA,SAAA3F,YAAA;IACA;IACAgG,QAAA,WAAAA,SAAAL,GAAA;MACA,KAAA/F,SAAA,CAAAqG,QAAA,GAAAJ,MAAA,CAAAA,MAAA,CAAAF,GAAA,SAAA3F,YAAA;IACA;IACAkG,QAAA,WAAAA,SAAAP,GAAA;MACA,KAAA/F,SAAA,CAAAuG,QAAA,GAAAN,MAAA,CAAAA,MAAA,CAAAF,GAAA,SAAA3F,YAAA;IACA;IACAoG,SAAA,WAAAA,UAAAT,GAAA;MACA,KAAA/F,SAAA,CAAAyG,SAAA,GAAAR,MAAA,CAAAA,MAAA,CAAAF,GAAA,SAAA3F,YAAA;IACA;IACAsG,SAAA,WAAAA,UAAAX,GAAA;MACA,KAAA/F,SAAA,CAAA2G,SAAA,GAAAV,MAAA,CAAAA,MAAA,CAAAF,GAAA,SAAA3F,YAAA;IACA;IACAwG,YAAA,WAAAA,aAAA/D,KAAA;MACA,KAAArC,kBAAA,GAAAyF,MAAA,CAAApD,KAAA;MACA,KAAAtC,cAAA,GAAA0F,MAAA,CACA,KAAA7F,YAAA,QAAAC,uBAAA,QAAAC,0BAAA,QAAAE,kBACA,EAAAqG,OAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,IAAAtH,IAAA;QACAmE,OAAA,OAAA5D,SAAA,CAAA4D,OAAA;QACAoD,EAAA,OAAAhH,SAAA,CAAAgH,EAAA;QACAC,MAAA,OAAAjH,SAAA,CAAAiH,MAAA;QACAC,UAAA,OAAAlH,SAAA,CAAAkH,UAAA;QACAhG,SAAA,OAAAlB,SAAA,CAAAkB;MACA;MACA,IAAAiG,gCAAA,EAAA1H,IAAA,EAAAiG,IAAA,WAAAC,QAAA;QACAoB,MAAA,CAAA9G,kBAAA,GAAA0F,QAAA,CAAAlG,IAAA,CAAA2H,SAAA;QACAL,MAAA,CAAA/G,SAAA,CAAAqH,eAAA,GAAA1B,QAAA,CAAAlG,IAAA,CAAA4H,eAAA;QACAN,MAAA,CAAA5G,iBAAA,GAAAwF,QAAA,CAAAlG,IAAA,CAAA6H,QAAA;QACAP,MAAA,CAAA3G,YAAA,GAAAuF,QAAA,CAAAlG,IAAA,CAAA8H,WAAA;QACAR,MAAA,CAAA1G,uBAAA,GAAAsF,QAAA,CAAAlG,IAAA,CAAA+H,WAAA;QACAT,MAAA,CAAAzG,0BAAA,GAAAqF,QAAA,CAAAlG,IAAA,CAAAgI,iBAAA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACAV,MAAA,CAAAxG,cAAA,GAAA0F,MAAA,CACAc,MAAA,CAAA3G,YAAA,GAAA2G,MAAA,CAAA1G,uBAAA,GAAA0G,MAAA,CAAAzG,0BAAA,GAAAyG,MAAA,CAAAvG,kBACA,EAAAqG,OAAA;MACA;IACA;IACAvB,WAAA,WAAAA,YAAA;MAAA,IAAAoC,MAAA;MACA,IAAAC,iCAAA,IAAAjC,IAAA,WAAAC,QAAA;QACA+B,MAAA,CAAAjH,WAAA,GAAAkF,QAAA,CAAAC,IAAA;MACA;IACA;IACAP,UAAA,WAAAA,WAAA;MAAA,IAAAuC,MAAA;MACA,KAAAlI,OAAA;MACA,IAAAmI,sBAAA;QAAAzF,MAAA;MAAA,GACAsD,IAAA,WAAAC,QAAA;QACAiC,MAAA,CAAAE,QAAA,GAAAnC,QAAA,CAAAC,IAAA;QACAgC,MAAA,CAAAlI,OAAA;MACA,GACAmG,KAAA;QACA+B,MAAA,CAAAlI,OAAA;MACA;IACA;IACA;IACAqI,MAAA,WAAAA,OAAAC,MAAA;MACA,IAAAA,MAAA;QACA,KAAAjI,WAAA;MACA;MACA,KAAAkI,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MAAA,IAAAC,gBAAA;MACA,CAAAA,gBAAA,QAAAC,KAAA,CAAAjF,IAAA,cAAAgF,gBAAA,eAAAA,gBAAA,CAAAE,WAAA;MACA,KAAAlF,IAAA;MACA,KAAAxC,WAAA,CAAAgC,SAAA;MACA,KAAAhC,WAAA,CAAAiC,SAAA;IACA;IACA,aACA0F,WAAA,WAAAA,YAAA;MACA,SAAA3H,WAAA,CAAAY,cAAA;QACA,KAAAZ,WAAA,CAAAa,SAAA,QAAAb,WAAA,CAAAY,cAAA;QACA,KAAAZ,WAAA,CAAAc,OAAA,QAAAd,WAAA,CAAAY,cAAA;MACA;MACA,KAAAZ,WAAA,CAAAC,OAAA;MACA,KAAAyE,OAAA;IACA;IACA,aACAkD,UAAA,WAAAA,WAAA;MACA,KAAA5H,WAAA,CAAAG,YAAA;MACA,KAAAH,WAAA,CAAA6H,MAAA;MACA,KAAA7H,WAAA,CAAAK,OAAA;MACA,KAAAL,WAAA,CAAAM,QAAA;MACA,KAAAN,WAAA,CAAAO,MAAA;MACA,KAAAP,WAAA,CAAAQ,SAAA;MACA,KAAAR,WAAA,CAAAS,YAAA;MACA,KAAAT,WAAA,CAAAU,YAAA;MACA,KAAAV,WAAA,CAAAW,cAAA;MACA,KAAAX,WAAA,CAAAY,cAAA;MACA,KAAAZ,WAAA,CAAAa,SAAA;MACA,KAAAb,WAAA,CAAAc,OAAA;MACA,KAAAd,WAAA,CAAAgC,SAAA;MACA,KAAAhC,WAAA,CAAAiC,SAAA;MACA,KAAA0F,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA;MACA;IAAA,CACA;IACA,aACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAC,IAAA;MACA,KAAA9I,KAAA;IACA;IACA,aACA+I,QAAA,WAAAA,SAAAF,GAAA;MAAA,IAAAG,MAAA;MACA;MACA,IAAArJ,IAAA;QACAwH,MAAA,EAAA0B,GAAA,CAAA1B;MACA;MACA,IAAA8B,wCAAA,EAAAtJ,IAAA,EAAAiG,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAlG,IAAA;UACAqJ,MAAA,CAAA9I,SAAA,GAAA2F,QAAA,CAAAlG,IAAA;UACAqJ,MAAA,CAAA9I,SAAA,CAAAkH,UAAA,GAAAyB,GAAA,CAAAK,WAAA;UACAF,MAAA,CAAA9I,SAAA,CAAAwH,WAAA,GAAA7B,QAAA,CAAAlG,IAAA,CAAAwJ,YAAA,GAAAtD,QAAA,CAAAlG,IAAA,CAAAwJ,YAAA,CAAAzB,WAAA;UACAsB,MAAA,CAAA7I,kBAAA,GAAA0F,QAAA,CAAAlG,IAAA,CAAAwJ,YAAA,GAAAtD,QAAA,CAAAlG,IAAA,CAAAwJ,YAAA,CAAA7B,SAAA;UACA0B,MAAA,CAAA5I,uBAAA,GAAAyI,GAAA,CAAAO,cAAA;UACAJ,MAAA,CAAA3I,iBAAA,GAAAwF,QAAA,CAAAlG,IAAA,CAAAwJ,YAAA,GAAAtD,QAAA,CAAAlG,IAAA,CAAAwJ,YAAA,CAAA3B,QAAA;UACAwB,MAAA,CAAA1I,YAAA,GAAAuF,QAAA,CAAAlG,IAAA,CAAAwJ,YAAA,GAAAtD,QAAA,CAAAlG,IAAA,CAAAwJ,YAAA,CAAA1B,WAAA;UACAuB,MAAA,CAAAzI,uBAAA,GAAAsF,QAAA,CAAAlG,IAAA,CAAAwJ,YAAA,GAAAtD,QAAA,CAAAlG,IAAA,CAAAwJ,YAAA,CAAAzB,WAAA;UACAsB,MAAA,CAAAxI,0BAAA,GAAAqF,QAAA,CAAAlG,IAAA,CAAAwJ,YAAA,GAAAtD,QAAA,CAAAlG,IAAA,CAAAwJ,YAAA,CAAAxB,iBAAA;UACAqB,MAAA,CAAAtI,kBAAA,GAAAmF,QAAA,CAAAlG,IAAA,CAAA0J,SAAA,GAAAxD,QAAA,CAAAlG,IAAA,CAAA0J,SAAA;UACAC,OAAA,CAAAC,GAAA,CAAAP,MAAA,CAAA1I,YAAA,EAAA0I,MAAA,CAAAzI,uBAAA,EAAAyI,MAAA,CAAAxI,0BAAA,EAAAwI,MAAA,CAAAtI,kBAAA;UACAsI,MAAA,CAAAvI,cAAA,GAAA0F,MAAA,CACA6C,MAAA,CAAA1I,YAAA,GAAA0I,MAAA,CAAAzI,uBAAA,GAAAyI,MAAA,CAAAxI,0BAAA,GAAAwI,MAAA,CAAAtI,kBACA,EAAAqG,OAAA;QACA;UACAiC,MAAA,CAAAQ,QAAA,CAAAX,GAAA;QACA;QACAG,MAAA,CAAA/I,WAAA;MACA;IACA;IACA,WACAwJ,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAArB,KAAA,CAAAjF,IAAA,CAAAuG,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAC,uBAAA,EAAAH,MAAA,CAAAtG,IAAA,EACAwC,IAAA,WAAAC,QAAA;YACA,IAAAA,QAAA,CAAAiE,IAAA;cACA,IAAAC,kBAAA;gBACA9E,OAAA;gBACA+E,IAAA;gBACAC,QAAA;cACA;cACAP,MAAA,CAAApE,OAAA;cACAoE,MAAA,CAAAvB,KAAA;YACA;cACA,IAAA4B,kBAAA;gBACA9E,OAAA,aAAAY,QAAA,CAAAZ,OAAA;gBACA+E,IAAA;gBACAC,QAAA;cACA;YACA;UACA,GACAlE,KAAA,WAAAmE,KAAA;YACA,IAAAH,kBAAA;cACA9E,OAAA,cAAAiF,KAAA,CAAAjF,OAAA;cACA+E,IAAA;cACAC,QAAA;YACA;UACA;QACA;UACAX,OAAA,CAAAC,GAAA;UACA;QACA;MACA;IACA;IACA;IACAY,YAAA,WAAAA,aAAAtG,YAAA;MACA,KAAAA,YAAA,GAAAA,YAAA;MACA,KAAAE,eAAA;IACA;IACAqG,WAAA,WAAAA,YAAAnJ,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAA+C,cAAA;IACA;IACA;IACAqG,OAAA,WAAAA,QAAAxB,GAAA;MACA,KAAA5E,UAAA,GAAA4E,GAAA;MACA,KAAAR,KAAA,CAAAiC,eAAA,CAAAC,aAAA;IACA;IACAC,uBAAA,WAAAA,wBAAA3B,GAAA;MACA,KAAA3E,cAAA,GAAA2E,GAAA,CAAA1B,MAAA;MACA,KAAAkB,KAAA,CAAAoC,mBAAA,CAAAC,UAAA;IACA;IACA;IACAC,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,qBAAA,EAAAC,sBAAA;MACA,KAAAC,qBAAA;MACA,KAAA1G,gBAAA,CAAAO,WAAA,GAAAoG,MAAA,GAAAH,qBAAA,QAAAI,MAAA,CAAAC,KAAA,CAAAC,IAAA,cAAAN,qBAAA,uBAAAA,qBAAA,CAAA1D,EAAA;MACA,KAAA9C,gBAAA,CAAAQ,aAAA,GAAAmG,MAAA,GAAAF,sBAAA,QAAAG,MAAA,CAAAC,KAAA,CAAAC,IAAA,cAAAL,sBAAA,uBAAAA,sBAAA,CAAAxL,IAAA;MACA;MACA,IAAA8L,KAAA,OAAAC,IAAA;MACA,KAAAhH,gBAAA,CAAAS,eAAA,GAAAsG,KAAA,CAAAE,WAAA,GAAAC,KAAA;MACA,KAAAnH,yBAAA;IACA;IACA;IACA2G,qBAAA,WAAAA,sBAAA;MAAA,IAAAS,qBAAA;MACA,KAAAnH,gBAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,WAAA;QACAC,kBAAA;QACAC,UAAA;QACAC,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,cAAA;MACA;MACA,KAAAM,eAAA;MACA,CAAAmG,qBAAA,QAAAlD,KAAA,CAAAjE,gBAAA,cAAAmH,qBAAA,eAAAA,qBAAA,CAAAjD,WAAA;IACA;IACA;IACAkD,kBAAA,WAAAA,mBAAA;MACA,KAAArH,yBAAA;MACA,KAAA2G,qBAAA;IACA;IACA;IACAW,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,KAAArD,KAAA,CAAAjE,gBAAA,CAAAuF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAA+B,iDAAA,EAAAD,MAAA,CAAAtH,gBAAA,EAAAwB,IAAA,WAAAgG,GAAA;YACA,IAAAA,GAAA,CAAA9B,IAAA;cACA4B,MAAA,CAAAG,QAAA,CAAAC,OAAA;cACAJ,MAAA,CAAAvH,yBAAA;cACAuH,MAAA,CAAAZ,qBAAA;YACA;cACAY,MAAA,CAAAG,QAAA,CAAA3B,KAAA,YAAA0B,GAAA,CAAAG,GAAA;YACA;UACA,GAAAhG,KAAA,WAAAmE,KAAA;YACAZ,OAAA,CAAAY,KAAA,UAAAA,KAAA;YACAwB,MAAA,CAAAG,QAAA,CAAA3B,KAAA;UACA;QACA;MACA;IACA;IACA;IACA8B,mBAAA,WAAAA,oBAAAC,IAAA,EAAAC,QAAA;MACA,KAAA9G,eAAA,GAAA8G,QAAA;MACA;MACA;MACA,IAAAD,IAAA,CAAAE,GAAA;QACA,KAAA/H,gBAAA,CAAAM,UAAA,GAAAuH,IAAA,CAAA5M,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}